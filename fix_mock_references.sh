#!/bin/bash

# <PERSON><PERSON>t to fix MockDBQueries references to use the shared test_utils version

echo "Fixing MockDBQueries references..."

# Update TUI test files
for file in tests/unit/tui/*.go; do
    if [ -f "$file" ]; then
        echo "Updating $file..."
        sed -i '' 's/new(MockDBQueries)/new(test_utils.MockDBQueries)/g' "$file"
    fi
done

# Update CLI test files  
for file in tests/unit/cli/*.go; do
    if [ -f "$file" ]; then
        echo "Updating $file..."
        sed -i '' 's/new(MockDBQueries)/new(test_utils.MockDBQueries)/g' "$file"
    fi
done

echo "Mock references updated!"
