#!/bin/bash

# Script to update all database connection patterns in commands.go
# This replaces the old pattern with the new helper function pattern

FILE="internal/cmd/commands.go"

echo "Updating database connection patterns in $FILE..."

# Create a backup
cp "$FILE" "$FILE.backup"

# Use sed to replace the pattern
# Replace the multi-line pattern:
# ctx := context.Background()
# conn, err := db.ConnectDB(dbConnectionString)
# if err != nil {
#     return fmt.Errorf("failed to connect to database: %w", err)
# }
# defer conn.Close(ctx)
# queries := db.New(conn)

# With:
# ctx := context.Background()
# queries, cleanup, err := connectDatabase(dbConnectionString)
# if err != nil {
#     return err
# }
# defer cleanup()

# Use perl for multi-line replacement
perl -i -0pe 's/(\s+)ctx := context\.Background\(\)\n\s+conn, err := db\.ConnectDB\(dbConnectionString\)\n\s+if err != nil \{\n\s+return fmt\.<PERSON><PERSON><PERSON>\("failed to connect to database: %w", err\)\n\s+\}\n\s+defer conn\.Close\(ctx\)\n\s+queries := db\.New\(conn\)/$1ctx := context.Background()\n$1queries, cleanup, err := connectDatabase(dbConnectionString)\n$1if err != nil {\n$1\treturn err\n$1}\n$1defer cleanup()/g' "$FILE"

echo "Database connection patterns updated!"
echo "Backup saved as $FILE.backup"
