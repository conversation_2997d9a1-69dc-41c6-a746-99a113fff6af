package integration

import (
	"context"
	"encoding/json"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/util"
)

func TestDatabase_CreateAndRetrieveMCPTool(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)

	// Create a test tool
	inputSchema := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"name": map[string]interface{}{
				"type":        "string",
				"description": "User name",
			},
		},
		"required": []string{"name"},
	}

	schemaBytes, err := json.Marshal(inputSchema)
	require.NoError(t, err)

	createParams := db.CreateMCPToolParams{
		ToolName:    "testTool",
		Description: pgtype.Text{String: "A test tool", Valid: true},
		InputSchema: schemaBytes,
	}

	// Create the tool
	createdTool, err := queries.CreateMCPTool(context.Background(), createParams)
	require.NoError(t, err)
	assert.NotZero(t, createdTool.ID)
	assert.Equal(t, "testTool", createdTool.ToolName)
	assert.Equal(t, "A test tool", createdTool.Description.String)
	assert.True(t, createdTool.Description.Valid)

	// Retrieve the tool by name
	retrievedTool, err := queries.GetMCPToolByName(context.Background(), "testTool")
	require.NoError(t, err)
	assert.Equal(t, createdTool.ID, retrievedTool.ID)
	assert.Equal(t, createdTool.ToolName, retrievedTool.ToolName)
	assert.Equal(t, createdTool.Description, retrievedTool.Description)

	// Verify input schema can be unmarshaled and converted
	var retrievedSchema interface{}
	err = json.Unmarshal(retrievedTool.InputSchema, &retrievedSchema)
	require.NoError(t, err)

	convertedSchema, err := util.ConvertJSONBTypes(retrievedSchema)
	require.NoError(t, err)
	assert.Equal(t, inputSchema, convertedSchema)
}

func TestDatabase_CreateMCPToolMapping(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)

	// First create a tool
	schemaBytes, _ := json.Marshal(map[string]interface{}{"type": "object"})
	toolParams := db.CreateMCPToolParams{
		ToolName:    "testTool",
		Description: pgtype.Text{String: "Test tool", Valid: true},
		InputSchema: schemaBytes,
	}

	createdTool, err := queries.CreateMCPTool(context.Background(), toolParams)
	require.NoError(t, err)

	// Create parameter mappings
	paramMappings := map[string]models.ParamMapping{
		"userId": {Source: "path", SourceName: "userId"},
		"filter": {Source: "query", SourceName: "filter"},
	}
	paramMappingsBytes, err := json.Marshal(paramMappings)
	require.NoError(t, err)

	// Create body mapping
	bodyMapping := models.BodyMapping{
		Strategy:         "merged",
		SourceProperties: []string{"name", "email"},
	}
	bodyMappingBytes, err := json.Marshal(bodyMapping)
	require.NoError(t, err)

	// Create the mapping
	mappingParams := db.CreateMCPToolMappingParams{
		McpToolID:     createdTool.ID,
		OpenapiPath:   "/users/{userId}",
		HttpMethod:    "POST",
		ParamMappings: paramMappingsBytes,
		BodyMapping:   bodyMappingBytes,
	}

	createdMapping, err := queries.CreateMCPToolMapping(context.Background(), mappingParams)
	require.NoError(t, err)
	assert.NotZero(t, createdMapping.ID)
	assert.Equal(t, createdTool.ID, createdMapping.McpToolID)
	assert.Equal(t, "/users/{userId}", createdMapping.OpenapiPath)
	assert.Equal(t, "POST", createdMapping.HttpMethod)

	// Verify mappings can be unmarshaled
	var retrievedParamMappings map[string]models.ParamMapping
	err = json.Unmarshal(createdMapping.ParamMappings, &retrievedParamMappings)
	require.NoError(t, err)
	assert.Equal(t, paramMappings, retrievedParamMappings)

	var retrievedBodyMapping models.BodyMapping
	err = json.Unmarshal(createdMapping.BodyMapping, &retrievedBodyMapping)
	require.NoError(t, err)
	assert.Equal(t, bodyMapping, retrievedBodyMapping)
}

func TestDatabase_GetToolWithMapping(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)

	// Create tool
	inputSchema := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"userId": map[string]interface{}{"type": "string"},
			"name":   map[string]interface{}{"type": "string"},
		},
	}
	schemaBytes, _ := json.Marshal(inputSchema)

	toolParams := db.CreateMCPToolParams{
		ToolName:    "updateUser",
		Description: pgtype.Text{String: "Update user", Valid: true},
		InputSchema: schemaBytes,
	}

	createdTool, err := queries.CreateMCPTool(context.Background(), toolParams)
	require.NoError(t, err)

	// Create mapping
	paramMappings := map[string]models.ParamMapping{
		"userId": {Source: "path", SourceName: "userId"},
	}
	paramMappingsBytes, _ := json.Marshal(paramMappings)

	bodyMapping := models.BodyMapping{
		Strategy:         "merged",
		SourceProperties: []string{"name"},
	}
	bodyMappingBytes, _ := json.Marshal(bodyMapping)

	mappingParams := db.CreateMCPToolMappingParams{
		McpToolID:     createdTool.ID,
		OpenapiPath:   "/users/{userId}",
		HttpMethod:    "PUT",
		ParamMappings: paramMappingsBytes,
		BodyMapping:   bodyMappingBytes,
	}

	_, err = queries.CreateMCPToolMapping(context.Background(), mappingParams)
	require.NoError(t, err)

	// Test GetToolWithMapping
	toolWithMapping, err := queries.GetToolWithMapping(context.Background(), "updateUser")
	require.NoError(t, err)

	assert.Equal(t, createdTool.ID, toolWithMapping.ToolID)
	assert.Equal(t, "updateUser", toolWithMapping.ToolName)
	assert.Equal(t, "Update user", toolWithMapping.Description.String)
	assert.Equal(t, "/users/{userId}", toolWithMapping.OpenapiPath)
	assert.Equal(t, "PUT", toolWithMapping.HttpMethod)

	// Verify schemas and mappings
	var retrievedSchema interface{}
	err = json.Unmarshal(toolWithMapping.InputSchema, &retrievedSchema)
	require.NoError(t, err)
	convertedSchema, err := util.ConvertJSONBTypes(retrievedSchema)
	require.NoError(t, err)
	assert.Equal(t, inputSchema, convertedSchema)

	var retrievedParamMappings map[string]models.ParamMapping
	err = json.Unmarshal(toolWithMapping.ParamMappings, &retrievedParamMappings)
	require.NoError(t, err)
	assert.Equal(t, paramMappings, retrievedParamMappings)

	var retrievedBodyMapping models.BodyMapping
	err = json.Unmarshal(toolWithMapping.BodyMapping, &retrievedBodyMapping)
	require.NoError(t, err)
	assert.Equal(t, bodyMapping, retrievedBodyMapping)
}

func TestDatabase_ListMCPTools(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)

	// Create multiple tools
	tools := []string{"toolA", "toolB", "toolC"}
	schemaBytes, _ := json.Marshal(map[string]interface{}{"type": "object"})

	for _, toolName := range tools {
		params := db.CreateMCPToolParams{
			ToolName:    toolName,
			Description: pgtype.Text{String: "Description for " + toolName, Valid: true},
			InputSchema: schemaBytes,
		}
		_, err := queries.CreateMCPTool(context.Background(), params)
		require.NoError(t, err)
	}

	// List all tools
	allTools, err := queries.ListMCPTools(context.Background())
	require.NoError(t, err)
	assert.Len(t, allTools, 3)

	// Verify tools are ordered by name
	toolNames := make([]string, len(allTools))
	for i, tool := range allTools {
		toolNames[i] = tool.ToolName
	}
	assert.Equal(t, []string{"toolA", "toolB", "toolC"}, toolNames)
}

func TestDatabase_DuplicateToolName(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)

	schemaBytes, _ := json.Marshal(map[string]interface{}{"type": "object"})

	// Create first tool
	params := db.CreateMCPToolParams{
		ToolName:    "duplicateTool",
		Description: pgtype.Text{String: "First tool", Valid: true},
		InputSchema: schemaBytes,
	}
	_, err = queries.CreateMCPTool(context.Background(), params)
	require.NoError(t, err)

	// Try to create second tool with same name
	params.Description = pgtype.Text{String: "Second tool", Valid: true}
	_, err = queries.CreateMCPTool(context.Background(), params)
	assert.Error(t, err) // Should fail due to unique constraint
}

func TestDatabase_ToolNotFound(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)

	// Try to get non-existent tool
	_, err = queries.GetMCPToolByName(context.Background(), "nonExistentTool")
	assert.Error(t, err)

	// Try to get tool with mapping that doesn't exist
	_, err = queries.GetToolWithMapping(context.Background(), "nonExistentTool")
	assert.Error(t, err)
}

func TestDatabase_ComplexJSONSchemas(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)

	// Create a complex schema
	complexSchema := map[string]interface{}{
		"type": "object",
		"properties": map[string]interface{}{
			"user": map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"profile": map[string]interface{}{
						"type": "object",
						"properties": map[string]interface{}{
							"preferences": map[string]interface{}{
								"type": "object",
								"properties": map[string]interface{}{
									"theme": map[string]interface{}{
										"type": "string",
										"enum": []string{"light", "dark"},
									},
									"notifications": map[string]interface{}{
										"type": "boolean",
									},
								},
							},
						},
					},
				},
			},
			"metadata": map[string]interface{}{
				"type": "array",
				"items": map[string]interface{}{
					"type": "string",
				},
			},
		},
		"required": []string{"user"},
	}

	schemaBytes, err := json.Marshal(complexSchema)
	require.NoError(t, err)

	params := db.CreateMCPToolParams{
		ToolName:    "complexTool",
		Description: pgtype.Text{String: "Complex schema tool", Valid: true},
		InputSchema: schemaBytes,
	}

	createdTool, err := queries.CreateMCPTool(context.Background(), params)
	require.NoError(t, err)

	// Retrieve and verify complex schema
	retrievedTool, err := queries.GetMCPToolByName(context.Background(), "complexTool")
	require.NoError(t, err)

	var retrievedSchema interface{}
	err = json.Unmarshal(retrievedTool.InputSchema, &retrievedSchema)
	require.NoError(t, err)
	convertedSchema, err := util.ConvertJSONBTypes(retrievedSchema)
	require.NoError(t, err)
	assert.Equal(t, complexSchema, convertedSchema)

	// Verify complex parameter and body mappings
	complexParamMappings := map[string]models.ParamMapping{
		"userId":        {Source: "path", SourceName: "user_id"},
		"apiKey":        {Source: "header", SourceName: "X-API-Key"},
		"includeHidden": {Source: "query", SourceName: "include_hidden"},
	}
	paramMappingsBytes, _ := json.Marshal(complexParamMappings)

	complexBodyMapping := models.BodyMapping{
		Strategy:         "merged",
		SourceProperties: []string{"user", "metadata", "settings"},
	}
	bodyMappingBytes, _ := json.Marshal(complexBodyMapping)

	mappingParams := db.CreateMCPToolMappingParams{
		McpToolID:     createdTool.ID,
		OpenapiPath:   "/users/{user_id}/complex",
		HttpMethod:    "POST",
		ParamMappings: paramMappingsBytes,
		BodyMapping:   bodyMappingBytes,
	}

	createdMapping, err := queries.CreateMCPToolMapping(context.Background(), mappingParams)
	require.NoError(t, err)

	// Verify complex mappings can be retrieved and unmarshaled
	var retrievedParamMappings map[string]models.ParamMapping
	err = json.Unmarshal(createdMapping.ParamMappings, &retrievedParamMappings)
	require.NoError(t, err)
	assert.Equal(t, complexParamMappings, retrievedParamMappings)

	var retrievedBodyMapping models.BodyMapping
	err = json.Unmarshal(createdMapping.BodyMapping, &retrievedBodyMapping)
	require.NoError(t, err)
	assert.Equal(t, complexBodyMapping, retrievedBodyMapping)
}