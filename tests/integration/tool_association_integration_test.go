package integration

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/ravan/suse-air/internal/db"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestToolAssociation_Integration(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)
	ctx := context.Background()

	// Create a test profile
	profile, err := queries.CreateProfile(ctx, db.CreateProfileParams{
		Name:        "TestProfile",
		Description: pgtype.Text{String: "Test profile for tool association", Valid: true},
		PathSegment: "test-profile",
	})
	require.NoError(t, err)

	// Create some test tools
	tool1, err := queries.CreateMCPTool(ctx, db.CreateMCPToolParams{
		ToolName:    "TestTool1",
		Description: pgtype.Text{String: "First test tool", Valid: true},
		InputSchema: []byte(`{"type": "object", "properties": {"message": {"type": "string"}}}`),
	})
	require.NoError(t, err)

	tool2, err := queries.CreateMCPTool(ctx, db.CreateMCPToolParams{
		ToolName:    "TestTool2",
		Description: pgtype.Text{String: "Second test tool", Valid: true},
		InputSchema: []byte(`{"type": "object", "properties": {"data": {"type": "string"}}}`),
	})
	require.NoError(t, err)

	// Test 1: Initially, profile should have no tools associated
	associatedTools, err := queries.ListMCPToolsByProfile(ctx, profile.Name)
	require.NoError(t, err)
	assert.Len(t, associatedTools, 0, "Profile should initially have no tools associated")

	// Test 2: List all available tools (this is what the 'a' key should show)
	allTools, err := queries.ListMCPTools(ctx)
	require.NoError(t, err)
	assert.Len(t, allTools, 2, "Should have 2 tools available in database")
	
	toolNames := make([]string, len(allTools))
	for i, tool := range allTools {
		toolNames[i] = tool.ToolName
	}
	assert.Contains(t, toolNames, "TestTool1")
	assert.Contains(t, toolNames, "TestTool2")

	// Test 3: Associate a tool with the profile
	profileTool, err := queries.CreateProfileTool(ctx, db.CreateProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool1.ID,
		Acl:       "EXECUTE",
	})
	require.NoError(t, err)
	assert.Equal(t, profile.ID, profileTool.ProfileID)
	assert.Equal(t, tool1.ID, profileTool.ToolID)
	assert.Equal(t, "EXECUTE", profileTool.Acl)

	// Test 4: Verify the tool is now associated with the profile
	associatedTools, err = queries.ListMCPToolsByProfile(ctx, profile.Name)
	require.NoError(t, err)
	assert.Len(t, associatedTools, 1, "Profile should now have 1 tool associated")
	assert.Equal(t, "TestTool1", associatedTools[0].ToolName)
	assert.Equal(t, "EXECUTE", associatedTools[0].Acl)

	// Test 5: Associate another tool
	_, err = queries.CreateProfileTool(ctx, db.CreateProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool2.ID,
		Acl:       "READ_ONLY",
	})
	require.NoError(t, err)

	// Test 6: Verify both tools are now associated
	associatedTools, err = queries.ListMCPToolsByProfile(ctx, profile.Name)
	require.NoError(t, err)
	assert.Len(t, associatedTools, 2, "Profile should now have 2 tools associated")

	// Verify the tools are ordered by name (as per the SQL query)
	assert.Equal(t, "TestTool1", associatedTools[0].ToolName)
	assert.Equal(t, "TestTool2", associatedTools[1].ToolName)

	// Test 7: Disassociate a tool
	err = queries.DeleteProfileTool(ctx, db.DeleteProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool1.ID,
	})
	require.NoError(t, err)

	// Test 8: Verify only one tool remains associated
	associatedTools, err = queries.ListMCPToolsByProfile(ctx, profile.Name)
	require.NoError(t, err)
	assert.Len(t, associatedTools, 1, "Profile should now have 1 tool associated after disassociation")
	assert.Equal(t, "TestTool2", associatedTools[0].ToolName)
	assert.Equal(t, "READ_ONLY", associatedTools[0].Acl)

	// Test 9: Verify all tools are still available in the database
	allTools, err = queries.ListMCPTools(ctx)
	require.NoError(t, err)
	assert.Len(t, allTools, 2, "All tools should still be available in database")
}
