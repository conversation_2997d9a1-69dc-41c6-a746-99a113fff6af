package integration

import (
	"bytes"
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/ravan/suse-air/internal/cmd"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/tui"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/urfave/cli/v2"

	"github.com/charmbracelet/bubbletea"
)

// Helper to run CLI commands for testing
func runCLICommand(t *testing.T, args []string) (string, string, error) {
	// Capture actual stdout and stderr since CLI commands use fmt.Printf
	oldStdout := os.Stdout
	oldStderr := os.Stderr

	rOut, wOut, _ := os.Pipe()
	rErr, wErr, _ := os.Pipe()

	os.Stdout = wOut
	os.Stderr = wErr

	var stdout, stderr bytes.Buffer

	// Read from pipes in goroutines
	go func() {
		defer rOut.Close()
		stdout.ReadFrom(rOut)
	}()

	go func() {
		defer rErr.Close()
		stderr.ReadFrom(rErr)
	}()

	app := &cli.App{
		Name:  "air",
		Usage: "A command-line tool for managing MCP tools and servers.",
		Commands: []*cli.Command{
			{
				Name:  "profile",
				Usage: "Manage profiles (groups of MCP tools).",
				Subcommands: []*cli.Command{
					{
						Name: "create",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name", Required: true},
							&cli.StringFlag{Name: "description"},
							&cli.StringFlag{Name: "path-segment", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileCreate(c)
						},
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileList(c)
						},
					},
					{
						Name: "get",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileGet(c)
						},
					},
					{
						Name: "update",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name", Required: true},
							&cli.StringFlag{Name: "new-name"},
							&cli.StringFlag{Name: "new-description"},
							&cli.StringFlag{Name: "new-path-segment"},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileUpdate(c)
						},
					},
					{
						Name: "delete",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunProfileDelete(c)
						},
					},
				},
			},
			{
				Name:  "tool",
				Usage: "Manage tool associations with profiles.",
				Subcommands: []*cli.Command{
					{
						Name: "associate",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "profile", Required: true},
							&cli.StringFlag{Name: "tool", Required: true},
							&cli.StringFlag{Name: "acl"},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunToolAssociate(c)
						},
					},
					{
						Name: "disassociate",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "profile", Required: true},
							&cli.StringFlag{Name: "tool", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunToolDisassociate(c)
						},
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "profile", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunToolList(c)
						},
					},
				},
			},
			{
				Name:  "user",
				Usage: "Manage users and their roles.",
				Subcommands: []*cli.Command{
					{
						Name: "create",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "username", Required: true},
							&cli.StringFlag{Name: "password", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunUserCreate(c)
						},
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunUserList(c)
						},
					},
					{
						Name: "assign-role",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "username", Required: true},
							&cli.StringFlag{Name: "role", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunUserAssignRole(c)
						},
					},
					{
						Name: "remove-role",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "username", Required: true},
							&cli.StringFlag{Name: "role", Required: true},
							&cli.StringFlag{Name: "db", Required: true},
						},
						Action: func(c *cli.Context) error {
							return cmd.RunUserRemoveRole(c)
						},
					},
				},
			},
		},
	}

	err := app.RunContext(context.Background(), append([]string{"air"}, args...))

	// Close write ends and restore stdout/stderr
	wOut.Close()
	wErr.Close()
	os.Stdout = oldStdout
	os.Stderr = oldStderr

	// Give goroutines time to finish reading
	time.Sleep(100 * time.Millisecond)

	return stdout.String(), stderr.String(), err
}

func TestCLI_ProfileCommands(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Test profile create
	out, errOut, err := runCLICommand(t, []string{"profile", "create", "--name", "test-profile", "--description", "A test profile", "--path-segment", "test-path", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile created successfully: ID=")
	assert.Contains(t, out, "Name=test-profile")
	assert.Contains(t, out, "PathSegment=test-path")

	// Test profile list
	out, errOut, err = runCLICommand(t, []string{"profile", "list", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profiles:")
	assert.Contains(t, out, "ID: 1, Name: test-profile, Description: A test profile, PathSegment: test-path")

	// Test profile get
	out, errOut, err = runCLICommand(t, []string{"profile", "get", "--name", "test-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile Details:")
	assert.Contains(t, out, "Name: test-profile")
	assert.Contains(t, out, "Description: A test profile")
	assert.Contains(t, out, "PathSegment: test-path")

	// Test profile update
	out, errOut, err = runCLICommand(t, []string{"profile", "update", "--name", "test-profile", "--new-name", "updated-profile", "--new-description", "Updated description", "--new-path-segment", "updated-path", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile updated successfully: ID=")
	assert.Contains(t, out, "Name=updated-profile")
	assert.Contains(t, out, "PathSegment=updated-path")

	// Verify update with get
	out, errOut, err = runCLICommand(t, []string{"profile", "get", "--name", "updated-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile Details:")
	assert.Contains(t, out, "Name: updated-profile")
	assert.Contains(t, out, "Description: Updated description")
	assert.Contains(t, out, "PathSegment: updated-path")

	// Test profile delete
	out, errOut, err = runCLICommand(t, []string{"profile", "delete", "--name", "updated-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Profile updated-profile deleted successfully.")

	// Verify delete with list
	out, errOut, err = runCLICommand(t, []string{"profile", "list", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "No profiles found.")
}

func TestCLI_ToolCommands(t *testing.T) {

	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Create a profile and a tool for association
	out, errOut, err := runCLICommand(t, []string{"profile", "create", "--name", "tool-profile", "--path-segment", "tool-path", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)

	// Manually insert a tool for testing tool association
	conn, err := db.ConnectDB(dbConnStr)
	require.NoError(t, err)
	defer conn.Close(context.Background())
	queries := db.New(conn)

	toolName := "test-tool-cli"
	toolDescription := "A tool for CLI testing"
	inputSchema := []byte(`{"type": "object"}`)
	createdTool, err := queries.CreateMCPTool(context.Background(), db.CreateMCPToolParams{
		ToolName:    toolName,
		Description: pgtype.Text{String: toolDescription, Valid: true},
		InputSchema: inputSchema,
	})
	require.NoError(t, err)

	// Test tool associate
	out, errOut, err = runCLICommand(t, []string{"tool", "associate", "--profile", "tool-profile", "--tool", toolName, "--acl", "EXECUTE", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Tool %s associated with profile tool-profile with ACL EXECUTE successfully.", toolName))

	// Test tool list
	out, errOut, err = runCLICommand(t, []string{"tool", "list", "--profile", "tool-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Tools for profile tool-profile:"))
	assert.Contains(t, out, fmt.Sprintf("ID: %d, Name: %s, Description: %s", createdTool.ID, toolName, toolDescription))

	// Test tool disassociate
	out, errOut, err = runCLICommand(t, []string{"tool", "disassociate", "--profile", "tool-profile", "--tool", toolName, "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Tool %s disassociated from profile tool-profile successfully.", toolName))

	// Verify disassociation with tool list
	out, errOut, err = runCLICommand(t, []string{"tool", "list", "--profile", "tool-profile", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "No tools found for profile tool-profile.")
}

func TestCLI_UserCommands(t *testing.T) {

	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Test user create
	out, errOut, err := runCLICommand(t, []string{"user", "create", "--username", "testuser", "--password", "password123", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "User created successfully: ID=")
	assert.Contains(t, out, "Username=testuser")

	// Test user list
	out, errOut, err = runCLICommand(t, []string{"user", "list", "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, "Users:")
	assert.Contains(t, out, "ID: 1, Username: testuser")

	// Create a role for testing user-role association
	conn, err := db.ConnectDB(dbConnStr)
	require.NoError(t, err)
	defer conn.Close(context.Background())
	queries := db.New(conn)

	roleName := "admin-role"
	_, err = queries.CreateRole(context.Background(), roleName)
	require.NoError(t, err)

	// Test user assign-role
	out, errOut, err = runCLICommand(t, []string{"user", "assign-role", "--username", "testuser", "--role", roleName, "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Role %s assigned to user testuser successfully.", roleName))

	// Test user remove-role
	out, errOut, err = runCLICommand(t, []string{"user", "remove-role", "--username", "testuser", "--role", roleName, "--db", dbConnStr})
	assert.NoError(t, err)
	assert.Empty(t, errOut)
	assert.Contains(t, out, fmt.Sprintf("Role %s removed from user testuser successfully.", roleName))
}

// TUI testing helper that creates a testable TUI model
func createTestTUIModel(t *testing.T, dbConnStr string) tea.Model {
	// Connect to database and create queries
	conn, err := db.ConnectDB(dbConnStr)
	require.NoError(t, err)
	queries := db.New(conn)

	// Create TUI model with injected queries for testing
	return tui.InitialModelWithQueries(queries)
}

// Helper to simulate key presses in TUI
func simulateKeyPress(model tea.Model, key string) (tea.Model, tea.Cmd) {
	var keyMsg tea.Msg

	switch key {
	case "enter":
		keyMsg = tea.KeyMsg{Type: tea.KeyEnter}
	case "esc":
		keyMsg = tea.KeyMsg{Type: tea.KeyEsc}
	case "up":
		keyMsg = tea.KeyMsg{Type: tea.KeyUp}
	case "down":
		keyMsg = tea.KeyMsg{Type: tea.KeyDown}
	case "tab":
		keyMsg = tea.KeyMsg{Type: tea.KeyTab}
	case "q":
		keyMsg = tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'q'}}
	case "c":
		keyMsg = tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'c'}}
	case "a":
		keyMsg = tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'a'}}
	case "d":
		keyMsg = tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'d'}}
	case "y":
		keyMsg = tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune{'y'}}
	default:
		// For text input
		keyMsg = tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune(key)}
	}

	return model.Update(keyMsg)
}

func TestTUI_ProfileManagement(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Create TUI model for testing
	model := createTestTUIModel(t, dbConnStr)

	// Initialize the model
	cmd := model.Init()
	if cmd != nil {
		// Execute any initialization commands
		model, _ = model.Update(cmd())
	}

	// Check initial view shows main menu
	view := model.View()
	assert.Contains(t, view, "Welcome to SUSE AIR TUI!")
	assert.Contains(t, view, "Profile Management")

	// Navigate to Profile Management (first option should be selected by default)
	// Send enter key to select Profile Management
	model, cmd = simulateKeyPress(model, "enter")
	if cmd != nil {
		// Execute the command to transition to profile view
		model, _ = model.Update(cmd())
	}

	// Should now be in profile management view
	view = model.View()
	// The view should show either profile list or indicate no profiles
	assert.True(t,
		assert.Contains(t, view, "No profiles found") ||
		assert.Contains(t, view, "Profiles:") ||
		assert.Contains(t, view, "Profile Management"),
		"Expected profile management view, got: %s", view)
}

func TestTUI_UserManagement(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Create TUI model for testing
	model := createTestTUIModel(t, dbConnStr)

	// Initialize the model
	cmd := model.Init()
	if cmd != nil {
		model, _ = model.Update(cmd())
	}

	// Navigate to User Management (third option)
	model, cmd = simulateKeyPress(model, "down") // Move to Tool Association
	if cmd != nil {
		model, _ = model.Update(cmd())
	}
	model, cmd = simulateKeyPress(model, "down") // Move to User Management
	if cmd != nil {
		model, _ = model.Update(cmd())
	}
	model, cmd = simulateKeyPress(model, "enter") // Select User Management
	if cmd != nil {
		model, _ = model.Update(cmd())
	}

	// Should now be in user management view
	view := model.View()
	// The view should show either user list or indicate no users
	assert.True(t,
		assert.Contains(t, view, "No users found") ||
		assert.Contains(t, view, "Users:") ||
		assert.Contains(t, view, "User Management"),
		"Expected user management view, got: %s", view)
}

func TestTUI_ToolAssociation(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	dbConnStr := testDB.ConnectionString

	// Pre-create a profile and tool for testing
	conn, err := db.ConnectDB(dbConnStr)
	require.NoError(t, err)
	defer conn.Close(context.Background())
	queries := db.New(conn)

	// Create a test profile
	_, err = queries.CreateProfile(context.Background(), db.CreateProfileParams{
		Name:        "test-profile",
		Description: pgtype.Text{String: "Test profile", Valid: true},
		PathSegment: "test-path",
	})
	require.NoError(t, err)

	// Create a test tool
	_, err = queries.CreateMCPTool(context.Background(), db.CreateMCPToolParams{
		ToolName:    "test-tool",
		Description: pgtype.Text{String: "Test tool", Valid: true},
		InputSchema: []byte(`{"type": "object"}`),
	})
	require.NoError(t, err)

	// Create TUI model for testing (using the same queries instance that has the data)
	model := tui.InitialModelWithQueries(queries)

	// Initialize the model
	cmd := model.Init()
	if cmd != nil {
		model, _ = model.Update(cmd())
	}

	// Navigate to Tool Association (second option)
	model, cmd = simulateKeyPress(model, "down") // Move to Tool Association
	if cmd != nil {
		model, _ = model.Update(cmd())
	}
	model, cmd = simulateKeyPress(model, "enter") // Select Tool Association
	if cmd != nil {
		// This should trigger the tool model's Init() which fetches profiles
		msg := cmd() // Execute the command to get the message
		model, cmd = model.Update(msg) // Process the message
		// If there's another command, execute it too
		if cmd != nil {
			msg = cmd()
			model, _ = model.Update(msg)
		}
	}

	// Should now be in tool association view
	view := model.View()
	// The view should show either profile list or tool association interface
	assert.True(t,
		assert.Contains(t, view, "test-profile") ||
		assert.Contains(t, view, "Tool Association") ||
		assert.Contains(t, view, "Select a Profile"),
		"Expected tool association view with test-profile, got: %s", view)
}
