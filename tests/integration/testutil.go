package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"os/exec"
	"strings"
	"testing"
	"time"

	"github.com/ory/dockertest/v3"
	"github.com/ory/dockertest/v3/docker"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
)

// TestDatabase represents a test database instance
type TestDatabase struct {
	ConnectionString string
	DatabaseType     string // "postgresql" or "sqlite"
	pool             *dockertest.Pool
	resource         *dockertest.Resource
	sqliteFile       string // Path to SQLite file for cleanup
}

// SetupTestDatabase creates a PostgreSQL test database using Docker
func SetupTestDatabase(t *testing.T) *TestDatabase {
	// Check if we should skip database tests
	if os.Getenv("SKIP_DB_TESTS") == "true" {
		t.Skip("Skipping database tests (SKIP_DB_TESTS=true)")
	}

	// Try to use existing database if TEST_DATABASE_URL is set
	if existingURL := os.Getenv("TEST_DATABASE_URL"); existingURL != "" {
		// Test the connection
		conn, err := db.ConnectDB(existingURL)
		if err == nil {
			// Connection successful, we can't call Close() on DBTX interface
			// but we know the connection works
			_ = conn
			return &TestDatabase{
				ConnectionString: existingURL,
				DatabaseType:     "postgresql", // Assume PostgreSQL for existing URL
			}
		}
		log.Printf("Could not connect to existing test database: %v", err)
	}

	// Create a new dockertest pool
	pool, err := dockertest.NewPool("")
	require.NoError(t, err, "Could not connect to Docker")

	// Pull PostgreSQL image and start container
	resource, err := pool.RunWithOptions(&dockertest.RunOptions{
		Repository: "postgres",
		Tag:        "13",
		Env: []string{
			"POSTGRES_PASSWORD=testpass",
			"POSTGRES_USER=testuser",
			"POSTGRES_DB=testdb",
			"POSTGRES_HOST_AUTH_METHOD=trust",
		},
	}, func(config *docker.HostConfig) {
		// Set AutoRemove to true so that stopped container goes away by itself
		config.AutoRemove = true
		config.RestartPolicy = docker.RestartPolicy{Name: "no"}
	})
	require.NoError(t, err, "Could not start PostgreSQL container")

	// Set container expiration to prevent resource leaks
	err = resource.Expire(300) // 5 minutes
	require.NoError(t, err)

	connectionString := fmt.Sprintf(
		"postgres://testuser:testpass@localhost:%s/testdb?sslmode=disable",
		resource.GetPort("5432/tcp"),
	)

	// Wait for database to be ready
	pool.MaxWait = 60 * time.Second
	err = pool.Retry(func() error {
		conn, err := db.ConnectDB(connectionString)
		if err != nil {
			return err
		}
		// Connection successful, we can't call Close() or Ping() on DBTX interface
		// but if ConnectDB succeeded, the database is ready
		_ = conn
		return nil
	})
	require.NoError(t, err, "Database never became ready")

	return &TestDatabase{
		ConnectionString: connectionString,
		DatabaseType:     "postgresql",
		pool:             pool,
		resource:         resource,
	}
}

// TearDown cleans up the test database
func (td *TestDatabase) TearDown() error {
	if td.DatabaseType == "postgresql" && td.pool != nil && td.resource != nil {
		return td.pool.Purge(td.resource)
	}
	if td.DatabaseType == "sqlite" && td.sqliteFile != "" {
		return os.Remove(td.sqliteFile)
	}
	return nil
}

// SetupSQLiteTestDatabase creates a SQLite test database using a temporary file
func SetupSQLiteTestDatabase(t *testing.T) *TestDatabase {
	// Check if we should skip database tests
	if os.Getenv("SKIP_DB_TESTS") == "true" {
		t.Skip("Skipping database tests (SKIP_DB_TESTS=true)")
	}

	// Create a temporary SQLite database file
	tempFile := fmt.Sprintf("/tmp/sair_test_%d.db", time.Now().UnixNano())

	connectionString := fmt.Sprintf("sqlite://%s", tempFile)

	// Test the connection
	conn, err := db.ConnectDB(connectionString)
	require.NoError(t, err, "Could not connect to SQLite test database")

	// Close the connection immediately - we just wanted to test it works
	// Note: We can't call Close() on the DBTX interface, so we'll rely on GC
	_ = conn

	return &TestDatabase{
		ConnectionString: connectionString,
		DatabaseType:     "sqlite",
		sqliteFile:       tempFile,
	}
}

func GetFreePort() (int, error) {
	addr, err := net.ResolveTCPAddr("tcp", "localhost:0")
	if err != nil {
		return 0, err
	}

	l, err := net.ListenTCP("tcp", addr)
	if err != nil {
		return 0, err
	}
	defer l.Close()
	return l.Addr().(*net.TCPAddr).Port, nil
}

func SetupMockAPIServer(t *testing.T) *httptest.Server {
	mux := http.NewServeMux()

	// Mock endpoint for sayHello
	mux.HandleFunc("/hello", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			t.Errorf("Expected POST request, got %s", r.Method)
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		// Read the request body
		var body string
		if err := json.NewDecoder(r.Body).Decode(&body); err != nil {
			t.Errorf("Failed to decode request body: %v", err)
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// Return a greeting
		response := fmt.Sprintf("Hello, %s!", body)
		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(response)
	})

	// Mock endpoint for getUser
	mux.HandleFunc("/users/", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "GET" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		// Extract user ID from path
		path := strings.TrimPrefix(r.URL.Path, "/users/")
		userID := strings.Split(path, "/")[0]

		user := map[string]interface{}{
			"id":   userID,
			"name": "Test User " + userID,
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(user)
	})

	// Mock endpoint for createUser
	mux.HandleFunc("/users", func(w http.ResponseWriter, r *http.Request) {
		if r.Method != "POST" {
			w.WriteHeader(http.StatusMethodNotAllowed)
			return
		}

		var user map[string]interface{}
		if err := json.NewDecoder(r.Body).Decode(&user); err != nil {
			w.WriteHeader(http.StatusBadRequest)
			return
		}

		// Add an ID to the user
		user["id"] = "new-user-123"

		w.Header().Set("Content-Type", "application/json")
		w.WriteHeader(http.StatusCreated)
		json.NewEncoder(w).Encode(user)
	})

	return httptest.NewServer(mux)
}

// RunMigrations applies the database schema to the test database
func (td *TestDatabase) RunMigrations(t *testing.T) {
	// Drop existing tables to ensure a clean state for each test
	td.DropTables(t)

	var cmd *exec.Cmd
	if td.DatabaseType == "postgresql" {
		// Run PostgreSQL migrations using goose
		cmd = exec.Command("goose", "-dir", "../../sql/migrations", "postgres", td.ConnectionString, "up")
	} else if td.DatabaseType == "sqlite" {
		// Run SQLite migrations using goose
		cmd = exec.Command("goose", "-dir", "../../sql/migrations_sqlite", "sqlite3", td.sqliteFile, "up")
	} else {
		t.Fatalf("Unknown database type: %s", td.DatabaseType)
	}

	cmd.Stdout = os.Stdout
	cmd.Stderr = os.Stderr
	err := cmd.Run()
	require.NoError(t, err, "Failed to run migrations with goose")
}

// DropTables drops all tables in the test database
func (td *TestDatabase) DropTables(t *testing.T) {
	if td.DatabaseType == "sqlite" {
		// For SQLite, just remove the file and recreate it
		if td.sqliteFile != "" {
			os.Remove(td.sqliteFile)
		}
		return
	}

	// PostgreSQL table dropping logic
	conn, err := db.ConnectDB(td.ConnectionString)
	require.NoError(t, err)
	// Note: We can't call Close() on DBTX interface, relying on GC

	// Get all table names
	rows, err := conn.Query(context.Background(), "SELECT tablename FROM pg_tables WHERE schemaname = 'public'")
	require.NoError(t, err)
	defer rows.Close()

	var tables []string
	for rows.Next() {
		var tableName string
		require.NoError(t, rows.Scan(&tableName))
		tables = append(tables, tableName)
	}

	// Drop tables in reverse order to handle foreign key constraints
	for i := len(tables) - 1; i >= 0; i-- {
		_, err := conn.Exec(context.Background(), fmt.Sprintf("DROP TABLE IF EXISTS %s CASCADE", tables[i]))
		require.NoError(t, err)
	}
}

// CleanupTables removes all data from test tables
func (td *TestDatabase) CleanupTables(t *testing.T) {
	conn, err := db.ConnectDB(td.ConnectionString)
	require.NoError(t, err)
	// Note: We can't call Close() on DBTX interface, relying on GC

	// Clean up in reverse order due to foreign key constraints
	// This works for both PostgreSQL and SQLite
	_, err = conn.Exec(context.Background(), "DELETE FROM user_roles")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM role_profiles")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM profile_tools")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM schema_connections")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM mcp_tool_mappings")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM openapi_operations")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM openapi_schemas")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM connections")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM users")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM roles")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM profiles")
	require.NoError(t, err)

	_, err = conn.Exec(context.Background(), "DELETE FROM mcp_tools")
	require.NoError(t, err)
}

// GetConnection returns a database connection for testing
func (td *TestDatabase) GetConnection(t *testing.T) *db.Queries {
	conn, err := db.ConnectDB(td.ConnectionString)
	require.NoError(t, err)

	// Note: In real usage, you'd want to close this connection
	// For tests, we'll let the test cleanup handle it
	return db.New(conn)
}

// TestDatabaseFromEnv creates a test database instance from environment variables
// This is useful for CI/CD environments where a database is already running
func TestDatabaseFromEnv() *TestDatabase {
	connectionString := os.Getenv("TEST_DATABASE_URL")
	if connectionString == "" {
		return nil
	}

	return &TestDatabase{
		ConnectionString: connectionString,
	}
}

// SkipIfNoDatabase skips the test if no test database is available
func SkipIfNoDatabase(t *testing.T) {
	if os.Getenv("SKIP_DB_TESTS") == "true" {
		t.Skip("Skipping database test (SKIP_DB_TESTS=true)")
	}

	if os.Getenv("TEST_DATABASE_URL") == "" && os.Getenv("DOCKER_HOST") == "" {
		// Check if Docker is available
		pool, err := dockertest.NewPool("")
		if err != nil {
			t.Skip("Skipping database test - Docker not available and no TEST_DATABASE_URL set")
		}
		pool.Client.Ping()
	}
}
