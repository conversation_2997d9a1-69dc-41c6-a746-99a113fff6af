package integration

import (
	"context"
	"fmt"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
)

func TestSQLite_BasicOperations(t *testing.T) {
	// Setup SQLite test database
	testDB := SetupSQLiteTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Test profile creation
	profile, err := queries.CreateProfile(ctx, db.CreateProfileParams{
		Name:        "test-profile",
		Description: pgtype.Text{String: "Test profile for SQLite", Valid: true},
		PathSegment: "test",
	})
	require.NoError(t, err)
	assert.Equal(t, "test-profile", profile.Name)
	assert.Equal(t, "test", profile.PathSegment)

	// Test profile retrieval
	retrievedProfile, err := queries.GetProfileByName(ctx, "test-profile")
	require.NoError(t, err)
	assert.Equal(t, profile.ID, retrievedProfile.ID)
	assert.Equal(t, profile.Name, retrievedProfile.Name)

	// Test profile listing
	profiles, err := queries.ListProfiles(ctx)
	require.NoError(t, err)
	assert.Len(t, profiles, 1)
	assert.Equal(t, "test-profile", profiles[0].Name)

	// Test user creation
	user, err := queries.CreateUser(ctx, db.CreateUserParams{
		Username:     "testuser",
		PasswordHash: "hashedpassword",
	})
	require.NoError(t, err)
	assert.Equal(t, "testuser", user.Username)

	// Test user retrieval
	retrievedUser, err := queries.GetUserByUsername(ctx, "testuser")
	require.NoError(t, err)
	assert.Equal(t, user.ID, retrievedUser.ID)
	assert.Equal(t, user.Username, retrievedUser.Username)

	// Test role creation
	role, err := queries.CreateRole(ctx, "test-role")
	require.NoError(t, err)
	assert.Equal(t, "test-role", role.Name)

	// Test role retrieval
	retrievedRole, err := queries.GetRoleByName(ctx, "test-role")
	require.NoError(t, err)
	assert.Equal(t, role.ID, retrievedRole.ID)
	assert.Equal(t, role.Name, retrievedRole.Name)
}

func TestSQLite_MCPToolOperations(t *testing.T) {
	// Setup SQLite test database
	testDB := SetupSQLiteTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Test MCP tool creation
	tool, err := queries.CreateMCPTool(ctx, db.CreateMCPToolParams{
		ToolName:    "test-tool",
		Description: pgtype.Text{String: "Test tool for SQLite", Valid: true},
		InputSchema: []byte(`{"type": "object", "properties": {"name": {"type": "string"}}}`),
	})
	require.NoError(t, err)
	assert.Equal(t, "test-tool", tool.ToolName)

	// Test tool retrieval
	retrievedTool, err := queries.GetMCPToolByName(ctx, "test-tool")
	require.NoError(t, err)
	assert.Equal(t, tool.ID, retrievedTool.ID)
	assert.Equal(t, tool.ToolName, retrievedTool.ToolName)

	// Test tool listing
	tools, err := queries.ListMCPTools(ctx)
	require.NoError(t, err)
	assert.Len(t, tools, 1)
	assert.Equal(t, "test-tool", tools[0].ToolName)

	// Test tool mapping creation
	mapping, err := queries.CreateMCPToolMapping(ctx, db.CreateMCPToolMappingParams{
		McpToolID:     tool.ID,
		OpenapiPath:   "/test",
		HttpMethod:    "POST",
		ParamMappings: []byte(`{"param1": "value1"}`),
		BodyMapping:   []byte(`{"body": "test"}`),
	})
	require.NoError(t, err)
	assert.Equal(t, tool.ID, mapping.McpToolID)
	assert.Equal(t, "/test", mapping.OpenapiPath)
	assert.Equal(t, "POST", mapping.HttpMethod)
}

func TestSQLite_ConnectionOperations(t *testing.T) {
	// Setup SQLite test database
	testDB := SetupSQLiteTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Test connection creation
	connection, err := queries.CreateConnection(ctx, db.CreateConnectionParams{
		Name:        "test-connection",
		Description: pgtype.Text{String: "Test connection for SQLite", Valid: true},
		BaseUrl:     "https://api.example.com",
		AuthType:    "bearer",
	})
	require.NoError(t, err)
	assert.Equal(t, "test-connection", connection.Name)
	assert.Equal(t, "https://api.example.com", connection.BaseUrl)
	assert.Equal(t, "bearer", connection.AuthType)

	// Test connection retrieval
	retrievedConnection, err := queries.GetConnectionByName(ctx, "test-connection")
	require.NoError(t, err)
	assert.Equal(t, connection.ID, retrievedConnection.ID)
	assert.Equal(t, connection.Name, retrievedConnection.Name)

	// Test connection listing
	connections, err := queries.ListConnections(ctx)
	require.NoError(t, err)
	assert.Len(t, connections, 1)
	assert.Equal(t, "test-connection", connections[0].Name)
}

func TestSQLite_ForeignKeyConstraints(t *testing.T) {
	// Setup SQLite test database
	testDB := SetupSQLiteTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Create a profile
	profile, err := queries.CreateProfile(ctx, db.CreateProfileParams{
		Name:        "test-profile",
		Description: pgtype.Text{String: "Test profile", Valid: true},
		PathSegment: "test",
	})
	require.NoError(t, err)

	// Create a tool
	tool, err := queries.CreateMCPTool(ctx, db.CreateMCPToolParams{
		ToolName:    "test-tool",
		Description: pgtype.Text{String: "Test tool", Valid: true},
		InputSchema: []byte(`{"type": "object"}`),
	})
	require.NoError(t, err)

	// Create a profile-tool association
	profileTool, err := queries.CreateProfileTool(ctx, db.CreateProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool.ID,
		Acl:       "EXECUTE",
	})
	require.NoError(t, err)
	assert.Equal(t, profile.ID, profileTool.ProfileID)
	assert.Equal(t, tool.ID, profileTool.ToolID)
	assert.Equal(t, "EXECUTE", profileTool.Acl)

	// Test that we can retrieve tools by profile
	toolsByProfile, err := queries.ListMCPToolsByProfile(ctx, "test-profile")
	require.NoError(t, err)
	assert.Len(t, toolsByProfile, 1)
	assert.Equal(t, "test-tool", toolsByProfile[0].ToolName)
	assert.Equal(t, "EXECUTE", toolsByProfile[0].Acl)
}

func TestSQLite_TransactionSupport(t *testing.T) {
	// Setup SQLite test database
	testDB := SetupSQLiteTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	queries := testDB.GetConnection(t)
	ctx := context.Background()

	// Test that multiple operations work correctly
	// (SQLite supports transactions, but we're testing basic multi-operation consistency)
	
	// Create multiple profiles
	for i := 0; i < 5; i++ {
		_, err := queries.CreateProfile(ctx, db.CreateProfileParams{
			Name:        fmt.Sprintf("profile-%d", i),
			Description: pgtype.Text{String: fmt.Sprintf("Profile %d", i), Valid: true},
			PathSegment: fmt.Sprintf("profile%d", i),
		})
		require.NoError(t, err)
	}

	// Verify all profiles were created
	profiles, err := queries.ListProfiles(ctx)
	require.NoError(t, err)
	assert.Len(t, profiles, 5)

	// Test cleanup works correctly
	testDB.CleanupTables(t)
	
	profiles, err = queries.ListProfiles(ctx)
	require.NoError(t, err)
	assert.Len(t, profiles, 0)
}
