package integration

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/util"
)

func TestConnectionManagement_Integration(t *testing.T) {
	// Setup test database
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()
	testDB.RunMigrations(t)

	ctx := context.Background()
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)
	connService := models.NewConnectionService(queries)

	t.Run("CreateConnection_Bearer", func(t *testing.T) {
		req := models.CreateConnectionRequest{
			Name:        "test-bearer",
			Description: "Test bearer auth connection",
			BaseURL:     "https://api.bearer.com",
			Auth: util.ConnectionAuth{
				Type:  "bearer",
				Token: "bearer-token-123",
			},
		}

		result, err := connService.CreateConnection(ctx, req)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test-bearer", result.Name)
		assert.Equal(t, "Test bearer auth connection", result.Description)
		assert.Equal(t, "https://api.bearer.com", result.BaseURL)
		assert.Equal(t, "bearer", result.Auth.Type)
		assert.Equal(t, "bearer-token-123", result.Auth.Token)
	})

	t.Run("CreateConnection_Basic", func(t *testing.T) {
		req := models.CreateConnectionRequest{
			Name:        "test-basic",
			Description: "Test basic auth connection",
			BaseURL:     "https://api.basic.com",
			Auth: util.ConnectionAuth{
				Type:     "basic",
				Username: "testuser",
				Password: "testpass",
			},
		}

		result, err := connService.CreateConnection(ctx, req)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test-basic", result.Name)
		assert.Equal(t, "basic", result.Auth.Type)
		assert.Equal(t, "testuser", result.Auth.Username)
		assert.Equal(t, "testpass", result.Auth.Password)
	})

	t.Run("CreateConnection_CustomHeader", func(t *testing.T) {
		req := models.CreateConnectionRequest{
			Name:        "test-custom",
			Description: "Test custom header auth connection",
			BaseURL:     "https://api.custom.com",
			Auth: util.ConnectionAuth{
				Type:        "custom_header",
				HeaderName:  "X-API-Key",
				HeaderValue: "custom-key-123",
			},
		}

		result, err := connService.CreateConnection(ctx, req)
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test-custom", result.Name)
		assert.Equal(t, "custom_header", result.Auth.Type)
		assert.Equal(t, "X-API-Key", result.Auth.HeaderName)
		assert.Equal(t, "custom-key-123", result.Auth.HeaderValue)
	})

	t.Run("GetConnection", func(t *testing.T) {
		result, err := connService.GetConnection(ctx, "test-bearer")
		require.NoError(t, err)
		assert.NotNil(t, result)
		assert.Equal(t, "test-bearer", result.Name)
		assert.Equal(t, "bearer", result.Auth.Type)
		assert.Equal(t, "bearer-token-123", result.Auth.Token)
	})

	t.Run("ListConnections", func(t *testing.T) {
		connections, err := connService.ListConnections(ctx)
		require.NoError(t, err)
		assert.Len(t, connections, 3)

		// Check that sensitive data is not included in list view
		for _, conn := range connections {
			assert.Empty(t, conn.Auth.Token)
			assert.Empty(t, conn.Auth.Password)
			assert.Empty(t, conn.Auth.HeaderValue)
		}
	})

	t.Run("DeleteConnection", func(t *testing.T) {
		err := connService.DeleteConnection(ctx, "test-custom")
		require.NoError(t, err)

		// Verify deletion
		_, err = connService.GetConnection(ctx, "test-custom")
		assert.Error(t, err)
	})
}

func TestSchemaConnectionAssociation_Integration(t *testing.T) {
	// Setup test database
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()
	testDB.RunMigrations(t)

	ctx := context.Background()
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)
	connService := models.NewConnectionService(queries)
	schemaService := models.NewSchemaService(queries)

	// Create test connection
	connReq := models.CreateConnectionRequest{
		Name:    "test-api",
		BaseURL: "https://api.test.com",
		Auth: util.ConnectionAuth{
			Type:  "bearer",
			Token: "test-token",
		},
	}
	_, err = connService.CreateConnection(ctx, connReq)
	require.NoError(t, err)

	// Create test schema
	schemaReq := models.CreateSchemaRequest{
		Name:     "test-schema",
		Filename: "test.json",
		Version:  "1.0.0",
	}
	_, err = schemaService.CreateSchema(ctx, schemaReq)
	require.NoError(t, err)

	t.Run("AssociateConnection", func(t *testing.T) {
		err := schemaService.AssociateConnection(ctx, "test-schema", "test-api", true)
		require.NoError(t, err)

		// Verify association
		schema, err := queries.GetOpenAPISchemaByName(ctx, "test-schema")
		require.NoError(t, err)

		connections, err := queries.ListConnectionsBySchema(ctx, schema.ID)
		require.NoError(t, err)
		assert.Len(t, connections, 1)
		assert.Equal(t, "test-api", connections[0].Name)
	})

	t.Run("DisassociateConnection", func(t *testing.T) {
		err := schemaService.DisassociateConnection(ctx, "test-schema", "test-api")
		require.NoError(t, err)

		// Verify disassociation
		schema, err := queries.GetOpenAPISchemaByName(ctx, "test-schema")
		require.NoError(t, err)

		connections, err := queries.ListConnectionsBySchema(ctx, schema.ID)
		require.NoError(t, err)
		assert.Len(t, connections, 0)
	})
}

func TestConnectionEncryption_Integration(t *testing.T) {
	// Setup test database
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()
	testDB.RunMigrations(t)

	ctx := context.Background()
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)
	connService := models.NewConnectionService(queries)

	t.Run("EncryptionDecryption", func(t *testing.T) {
		// Create connection with sensitive data
		req := models.CreateConnectionRequest{
			Name:    "encryption-test",
			BaseURL: "https://api.secure.com",
			Auth: util.ConnectionAuth{
				Type:     "basic",
				Username: "secureuser",
				Password: "supersecretpassword",
			},
		}

		// Create connection
		result, err := connService.CreateConnection(ctx, req)
		require.NoError(t, err)

		// Verify that the password is correctly encrypted and decrypted
		assert.Equal(t, "supersecretpassword", result.Auth.Password)

		// Get connection again to verify persistence
		retrieved, err := connService.GetConnection(ctx, "encryption-test")
		require.NoError(t, err)
		assert.Equal(t, "supersecretpassword", retrieved.Auth.Password)

		// Verify that the password is actually encrypted in the database
		dbConn, err := queries.GetConnectionByName(ctx, "encryption-test")
		require.NoError(t, err)
		assert.NotEmpty(t, dbConn.AuthPasswordEncrypted)
		assert.NotEqual(t, "supersecretpassword", string(dbConn.AuthPasswordEncrypted))
	})
}

func TestToolWithConnectionInfo_Integration(t *testing.T) {
	// Setup test database
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()
	testDB.RunMigrations(t)

	ctx := context.Background()
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)

	// Create test data similar to what the convert command would create
	// 1. Create schema
	schema, err := queries.CreateOpenAPISchema(ctx, db.CreateOpenAPISchemaParams{
		Name:     "test-api",
		Filename: "test.json",
	})
	require.NoError(t, err)

	// 2. Create operation
	operation, err := queries.CreateOpenAPIOperation(ctx, db.CreateOpenAPIOperationParams{
		SchemaID:    schema.ID,
		OperationID: "testOperation",
		Path:        "/test",
		Method:      "POST",
	})
	require.NoError(t, err)

	// 3. Create tool
	tool, err := queries.CreateMCPTool(ctx, db.CreateMCPToolParams{
		ToolName:    "TestTool",
		InputSchema: []byte(`{"type": "object"}`),
	})
	require.NoError(t, err)

	// 4. Create mapping
	_, err = queries.CreateMCPToolMapping(ctx, db.CreateMCPToolMappingParams{
		McpToolID:     tool.ID,
		OpenapiPath:   "/test",
		HttpMethod:    "POST",
		ParamMappings: []byte(`{}`),
		BodyMapping:   []byte(`{}`),
		OperationID:   pgtype.Int8{Int64: operation.ID, Valid: true},
	})
	require.NoError(t, err)

	// 5. Create connection
	connection, err := queries.CreateConnection(ctx, db.CreateConnectionParams{
		Name:     "test-conn",
		BaseUrl:  "https://api.test.com",
		AuthType: "bearer",
	})
	require.NoError(t, err)

	// 6. Associate connection with schema
	_, err = queries.CreateSchemaConnection(ctx, db.CreateSchemaConnectionParams{
		SchemaID:     schema.ID,
		ConnectionID: connection.ID,
		IsDefault:    true,
	})
	require.NoError(t, err)

	t.Run("GetToolWithConnectionInfo", func(t *testing.T) {
		toolInfo, err := queries.GetToolWithConnectionInfo(ctx, "TestTool")
		require.NoError(t, err)

		assert.Equal(t, "TestTool", toolInfo.ToolName)
		assert.Equal(t, "test-conn", toolInfo.ConnectionName.String)
		assert.Equal(t, "https://api.test.com", toolInfo.BaseUrl.String)
		assert.Equal(t, "bearer", toolInfo.AuthType.String)
		assert.Equal(t, "/test", toolInfo.OpenapiPath)
		assert.Equal(t, "POST", toolInfo.HttpMethod)
	})
}
