package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
)

func TestEndToEnd_ConvertAndExecute(t *testing.T) {
	// Setup test database
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	// Build the application
	binaryPath := buildApplication(t)
	defer os.Remove(binaryPath)

	// Setup mock HTTP server for API calls
	mockServer := SetupMockAPIServer(t)
	defer mockServer.Close()

	// Test the convert command
	t.Run("Convert", func(t *testing.T) {
		testConvertCommand(t, binaryPath, testDB.ConnectionString)

		// Verify tools were created in database after convert command
		conn, err := db.ConnectDB(testDB.ConnectionString)
		require.NoError(t, err)

		queries := db.New(conn)
		tools, err := queries.ListMCPTools(context.Background())
		require.NoError(t, err)
		assert.GreaterOrEqual(t, len(tools), 3, "Expected at least 3 tools after conversion")

		toolNames := make([]string, len(tools))
		for i, tool := range tools {
			toolNames[i] = tool.ToolName
		}
		assert.Contains(t, toolNames, "SayHello")
		assert.Contains(t, toolNames, "GetUser")
		assert.Contains(t, toolNames, "CreateUser")
	})

	// Introduce a small delay to ensure database writes are committed
	time.Sleep(100 * time.Millisecond)

	// Test the execute command
	t.Run("Execute", func(t *testing.T) {
		testExecuteCommand(t, binaryPath, testDB.ConnectionString, mockServer.URL)
	})
}

func TestEndToEnd_ConvertCommand(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	binaryPath := buildApplication(t)
	defer os.Remove(binaryPath)

	// Test convert with simple OpenAPI spec
	specPath := getTestSpecPath(t, "simple.json")
	cmd := exec.Command(binaryPath, "convert",
		"-file", specPath,
		"-db", testDB.ConnectionString)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	if err != nil {
		t.Logf("Command failed with stdout: %s", stdout.String())
		t.Logf("Command failed with stderr: %s", stderr.String())
		require.NoError(t, err)
	}

	// Verify output
	output := stderr.String()
	assert.Contains(t, output, "Running in CONVERT mode")
	assert.Contains(t, output, "Saved")
	assert.Contains(t, output, "tools with mappings")

	// Verify tools were created in database
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)
	tools, err := queries.ListMCPTools(context.Background())
	require.NoError(t, err)
	assert.GreaterOrEqual(t, len(tools), 3) // Should have at least 3 tools from simple.json

	// Verify specific tools exist
	toolNames := make([]string, len(tools))
	for i, tool := range tools {
		toolNames[i] = tool.ToolName
	}
	assert.Contains(t, toolNames, "SayHello")
	assert.Contains(t, toolNames, "GetUser")
	assert.Contains(t, toolNames, "CreateUser")
}

func TestEndToEnd_ExecuteCommand(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	binaryPath := buildApplication(t)
	defer os.Remove(binaryPath)

	// Setup mock server
	mockServer := SetupMockAPIServer(t)
	defer mockServer.Close()

	// First convert the spec
	specPath := getTestSpecPath(t, "simple.json")
	convertCmd := exec.Command(binaryPath, "convert",
		"-file", specPath,
		"-db", testDB.ConnectionString)
	err := convertCmd.Run()
	require.NoError(t, err)

	// Test execute command with sayHello tool
	executeCmd := exec.Command(binaryPath, "execute",
		"-tool", "SayHello",
		"-args", `{"body": "Integration Test"}`,
		"-base-url", mockServer.URL,
		"-db", testDB.ConnectionString)

	var stdout, stderr bytes.Buffer
	executeCmd.Stdout = &stdout
	executeCmd.Stderr = &stderr

	err = executeCmd.Run()
	if err != nil {
		t.Logf("Execute command failed with stdout: %s", stdout.String())
		t.Logf("Execute command failed with stderr: %s", stderr.String())
		require.NoError(t, err)
	}

	// Verify output
	output := stderr.String()
	assert.Contains(t, output, "Running in EXECUTE mode")
	assert.Contains(t, output, "Fetching mapping for tool: SayHello")
	assert.Contains(t, output, "Building HTTP request")
	assert.Contains(t, output, "Executing request: POST")
	assert.Contains(t, output, "API Response Status: 200")
	assert.Contains(t, stdout.String(), "Hello, Integration Test!")
}

func TestEndToEnd_InvalidCommands(t *testing.T) {
	binaryPath := buildApplication(t)
	defer os.Remove(binaryPath)

	testCases := []struct {
		name           string
		args           []string
		expectedStdout string
		expectedStderr string
	}{
		{
			name:           "No subcommand",
			args:           []string{},
			expectedStdout: "",
			expectedStderr: "A command-line tool for managing MCP tools and servers.",
		},

		{
			name:           "Convert without file",
			args:           []string{"convert", "-file", "dummy"},
			expectedStdout: "",
			expectedStderr: "failed to read file dummy: open dummy: no such file or directory",
		},
		{
			name:           "Execute without tool",
			args:           []string{"execute", "-base-url", "http://example.com", "-tool", "dummy"},
			expectedStdout: "",
			expectedStderr: "error fetching tool 'dummy' from database: no rows in result set",
		},
		{
			name:           "Execute without base-url",
			args:           []string{"execute", "-tool", "test"},
			expectedStdout: "",
			expectedStderr: "Required flag \"base-url\" not set",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			testDB := SetupTestDatabase(t)
			defer testDB.TearDown()
			testDB.RunMigrations(t)
			defer testDB.CleanupTables(t)

			var cmd *exec.Cmd
			if tc.name == "No subcommand" {
				cmd = exec.Command(binaryPath, tc.args...)
			} else {
				cmd = exec.Command(binaryPath, append(tc.args, "-db", testDB.ConnectionString)...)
			}
			var stdout, stderr bytes.Buffer
			cmd.Stdout = &stdout
			cmd.Stderr = &stderr

			err := cmd.Run()

			if tc.name == "No subcommand" {
				assert.NoError(t, err) // Command should succeed
				assert.Contains(t, stdout.String(), tc.expectedStdout)
			} else {
				assert.Error(t, err) // Command should fail
				assert.Contains(t, stderr.String(), tc.expectedStderr)
			}
		})
	}
}

func TestEndToEnd_ComplexOpenAPISpec(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	binaryPath := buildApplication(t)
	defer os.Remove(binaryPath)

	// Test convert with complex OpenAPI spec (with $refs)
	specPath := getTestSpecPath(t, "complex_with_refs.json")
	cmd := exec.Command(binaryPath, "convert",
		"-file", specPath,
		"-db", testDB.ConnectionString)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	t.Logf("Convert command stdout: %s", stdout.String())
	t.Logf("Convert command stderr: %s", stderr.String())
	require.NoError(t, err)

	// Verify tools were created
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)
	tools, err := queries.ListMCPTools(context.Background())
	require.NoError(t, err)
	assert.GreaterOrEqual(t, len(tools), 3) // Should have tools from complex spec

	// Verify that $refs were resolved by checking a tool with complex schema
	createUserTool, err := queries.GetMCPToolByName(context.Background(), "CreateUser")
	require.NoError(t, err)

	var inputSchema map[string]interface{}
	err = json.Unmarshal(createUserTool.InputSchema, &inputSchema)
	require.NoError(t, err)

	t.Logf("createUserTool.InputSchema: %s", string(createUserTool.InputSchema))
	t.Logf("inputSchema: %+v", inputSchema)

	bodySchema, ok := inputSchema["properties"].(map[string]interface{})["body"]
	require.True(t, ok, "inputSchema should have a 'body' field")

	bodyProperties, ok := bodySchema.(map[string]interface{})["properties"]
	require.True(t, ok, "bodySchema should have a 'properties' field")

	properties, ok := bodyProperties.(map[string]interface{})
	require.True(t, ok, "bodyProperties should be of type map[string]interface{}")

	// Should have profile property resolved from $ref
	assert.Contains(t, properties, "profile")
	profileProp, ok := properties["profile"].(map[string]interface{})
	require.True(t, ok, "profile property should be of type map[string]interface{}")
	assert.Equal(t, "object", profileProp["type"])
}

func TestEndToEnd_EdgeCases(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	binaryPath := buildApplication(t)
	defer os.Remove(binaryPath)

	// Test convert with edge cases spec
	specPath := getTestSpecPath(t, "edge_cases.json")
	cmd := exec.Command(binaryPath, "convert",
		"-file", specPath,
		"-db", testDB.ConnectionString)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	t.Logf("Convert command stdout: %s", stdout.String())
	t.Logf("Convert command stderr: %s", stderr.String())
	require.NoError(t, err)

	// Should handle edge cases gracefully
	output := stderr.String()
	assert.Contains(t, output, "Saved")

	// Verify tools were created despite edge cases
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)
	tools, err := queries.ListMCPTools(context.Background())
	require.NoError(t, err)
	assert.GreaterOrEqual(t, len(tools), 1) // Should have at least one tool
}

// Helper functions

func buildApplication(t *testing.T) string {
	// Build the application binary for testing
	binaryPath := filepath.Join(os.TempDir(), fmt.Sprintf("sair-test-%d", time.Now().UnixNano()))

	cmd := exec.Command("go", "build", "-o", binaryPath, "../../cmd/air")
	err := cmd.Run()
	require.NoError(t, err, "Failed to build application")

	return binaryPath
}

func getTestSpecPath(t *testing.T, filename string) string {
	specPath := filepath.Join("..", "testdata", "openapi_specs", filename)
	_, err := os.Stat(specPath)
	require.NoError(t, err, "Test spec file not found: %s", specPath)
	return specPath
}

func testConvertCommand(t *testing.T, binaryPath, connectionString string) {
	specPath := getTestSpecPath(t, "simple.json")
	cmd := exec.Command(binaryPath, "convert",
		"-file", specPath,
		"-db", connectionString)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	t.Logf("Convert command stdout: %s", stdout.String())
	t.Logf("Convert command stderr: %s", stderr.String())
	require.NoError(t, err, "Convert command should succeed")
}

func testExecuteCommand(t *testing.T, binaryPath, connectionString, baseURL string) {
	cmd := exec.Command(binaryPath, "execute",
		"-tool", "SayHello",
		"-args", `{"body": "Integration Test"}`,
		"-base-url", baseURL,
		"-db", connectionString)

	var stdout, stderr bytes.Buffer
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	t.Logf("Execute command stdout: %s", stdout.String())
	t.Logf("Execute command stderr: %s", stderr.String())
	require.NoError(t, err, "Execute command should succeed")

	output := stdout.String()
	assert.Contains(t, output, "Hello, Integration Test!")
}
