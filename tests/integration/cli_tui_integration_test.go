package integration

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/tui"
)

// setupTestDB creates a clean test database for each test
func setupTestDB(t *testing.T, connectionString string) (*pgx.Conn, db.Querier) {
	// Connect to the test database
	conn, err := pgx.Connect(context.Background(), connectionString)
	require.NoError(t, err, "Failed to connect to test database")

	// Clean up any existing data
	_, err = conn.Exec(context.Background(), `
		TRUNCATE TABLE user_roles, users, roles, profile_tools, mcp_tool_mappings, mcp_tools, profiles RESTART IDENTITY CASCADE;
	`)
	require.NoError(t, err, "Failed to clean test database")

	queries := db.New(conn)
	return conn, queries
}

// teardownTestDB closes the database connection
func teardownTestDB(conn *pgx.Conn) {
	if conn != nil {
	}
}

func TestProfileCRUDIntegration(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	conn, queries := setupTestDB(t, testDB.ConnectionString)
	defer teardownTestDB(conn)

	ctx := context.Background()

	// Test 1: Create Profile
	t.Run("CreateProfile", func(t *testing.T) {
		profile, err := queries.CreateProfile(ctx, db.CreateProfileParams{
			Name:        "integration-test-profile",
			Description: pgtype.Text{String: "Integration test profile", Valid: true},
			PathSegment: "integration-test",
		})
		require.NoError(t, err)
		assert.Equal(t, "integration-test-profile", profile.Name)
		assert.Equal(t, "integration-test", profile.PathSegment)
		assert.True(t, profile.Description.Valid)
		assert.Equal(t, "Integration test profile", profile.Description.String)
	})

	// Test 2: List Profiles
	t.Run("ListProfiles", func(t *testing.T) {
		profiles, err := queries.ListProfiles(ctx)
		require.NoError(t, err)
		assert.Len(t, profiles, 1)
		assert.Equal(t, "integration-test-profile", profiles[0].Name)
	})

	// Test 3: Get Profile by Path Segment
	t.Run("GetProfileByPathSegment", func(t *testing.T) {
		profile, err := queries.GetProfileByPathSegment(ctx, "integration-test")
		require.NoError(t, err)
		assert.Equal(t, "integration-test-profile", profile.Name)
		assert.Equal(t, "integration-test", profile.PathSegment)
	})

	// Test 4: Update Profile
	t.Run("UpdateProfile", func(t *testing.T) {
		// First get the profile ID
		profile, err := queries.GetProfileByPathSegment(ctx, "integration-test")
		require.NoError(t, err)

		updatedProfile, err := queries.UpdateProfile(ctx, db.UpdateProfileParams{
			ID:          profile.ID,
			Name:        "updated-integration-profile",
			Description: pgtype.Text{String: "Updated integration test profile", Valid: true},
			PathSegment: "updated-integration",
		})
		require.NoError(t, err)
		assert.Equal(t, "updated-integration-profile", updatedProfile.Name)
		assert.Equal(t, "updated-integration", updatedProfile.PathSegment)
		assert.Equal(t, "Updated integration test profile", updatedProfile.Description.String)
	})

	// Test 5: Delete Profile
	t.Run("DeleteProfile", func(t *testing.T) {
		// First get the profile ID
		profile, err := queries.GetProfileByPathSegment(ctx, "updated-integration")
		require.NoError(t, err)

		err = queries.DeleteProfile(ctx, profile.ID)
		require.NoError(t, err)

		// Verify profile is deleted
		profiles, err := queries.ListProfiles(ctx)
		require.NoError(t, err)
		assert.Len(t, profiles, 0)
	})
}

func TestToolManagementIntegration(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	conn, queries := setupTestDB(t, testDB.ConnectionString)
	defer teardownTestDB(conn)

	ctx := context.Background()

	// Setup: Create a profile first
	profile, err := queries.CreateProfile(ctx, db.CreateProfileParams{
		Name:        "tool-test-profile",
		Description: pgtype.Text{String: "Profile for tool testing", Valid: true},
		PathSegment: "tool-test",
	})
	require.NoError(t, err)

	// Test 1: Create MCP Tool
	t.Run("CreateMCPTool", func(t *testing.T) {
		tool, err := queries.CreateMCPTool(ctx, db.CreateMCPToolParams{
			ToolName:    "TestTool",
			Description: pgtype.Text{String: "A test tool for integration testing", Valid: true},
			InputSchema: []byte(`{"type":"object","properties":{"message":{"type":"string"}},"required":["message"]}`),
		})
		require.NoError(t, err)
		assert.Equal(t, "TestTool", tool.ToolName)
		assert.True(t, tool.Description.Valid)
		assert.Equal(t, "A test tool for integration testing", tool.Description.String)
	})

	// Test 2: List MCP Tools
	t.Run("ListMCPTools", func(t *testing.T) {
		tools, err := queries.ListMCPTools(ctx)
		require.NoError(t, err)
		assert.Len(t, tools, 1)
		assert.Equal(t, "TestTool", tools[0].ToolName)
	})

	// Test 3: Create Tool Mapping
	t.Run("CreateMCPToolMapping", func(t *testing.T) {
		// Get the tool ID
		tools, err := queries.ListMCPTools(ctx)
		require.NoError(t, err)
		require.Len(t, tools, 1)
		toolID := tools[0].ID

		mapping, err := queries.CreateMCPToolMapping(ctx, db.CreateMCPToolMappingParams{
			McpToolID:     toolID,
			OpenapiPath:   "/test",
			HttpMethod:    "POST",
			ParamMappings: []byte(`{}`),
			BodyMapping:   []byte(`{"message": "{{.message}}"}`),
		})
		require.NoError(t, err)
		assert.Equal(t, toolID, mapping.McpToolID)
		assert.Equal(t, "/test", mapping.OpenapiPath)
		assert.Equal(t, "POST", mapping.HttpMethod)
	})

	// Test 4: Create Profile Tool Association
	t.Run("CreateProfileTool", func(t *testing.T) {
		// Get the tool ID
		tools, err := queries.ListMCPTools(ctx)
		require.NoError(t, err)
		require.Len(t, tools, 1)
		toolID := tools[0].ID

		profileTool, err := queries.CreateProfileTool(ctx, db.CreateProfileToolParams{
			ProfileID: profile.ID,
			ToolID:    toolID,
			Acl:       "EXECUTE",
		})
		require.NoError(t, err)
		assert.Equal(t, profile.ID, profileTool.ProfileID)
		assert.Equal(t, toolID, profileTool.ToolID)
		assert.Equal(t, "EXECUTE", profileTool.Acl)
	})

	// Test 5: List Tools by Profile
	t.Run("ListMCPToolsByProfile", func(t *testing.T) {
		tools, err := queries.ListMCPToolsByProfile(ctx, profile.Name)
		require.NoError(t, err)
		assert.Len(t, tools, 1)
		assert.Equal(t, "TestTool", tools[0].ToolName)
		assert.Equal(t, "EXECUTE", tools[0].Acl)
	})

	// Test 6: Get Tool with Mapping
	t.Run("GetToolWithMapping", func(t *testing.T) {
		toolWithMapping, err := queries.GetToolWithMapping(ctx, "TestTool")
		require.NoError(t, err)
		assert.Equal(t, "TestTool", toolWithMapping.ToolName)
		assert.Equal(t, "/test", toolWithMapping.OpenapiPath)
		assert.Equal(t, "POST", toolWithMapping.HttpMethod)
	})
}

func TestTUIModelIntegration(t *testing.T) {
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	conn, queries := setupTestDB(t, testDB.ConnectionString)
	defer teardownTestDB(conn)

	// Test TUI Profile Model with real database
	t.Run("TUIProfileModelIntegration", func(t *testing.T) {
		// Initialize TUI model with real database
		model := tui.InitialModelWithQueries(queries)
		tuiModel := model.(tui.Model)

		// Verify initial state
		assert.Contains(t, tuiModel.View(), "Welcome to SUSE AIR TUI!")
		assert.Contains(t, tuiModel.View(), "Profile Management")
	})
}
