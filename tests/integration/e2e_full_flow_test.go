package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"testing"
	"time"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/server"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/jackc/pgx/v5/pgtype"
)

func TestE2E_FullFlow(t *testing.T) {
	SkipIfNoDatabase(t)

	// Setup test database
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	// Insert a dummy profile
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)

	queries := db.New(conn)
	profileName := "full-flow-profile"
	profile, err := queries.CreateProfile(context.Background(), db.CreateProfileParams{
		Name:        profileName,
		PathSegment: profileName,
	})
	require.NoError(t, err)

	// Insert a dummy tool
	toolName := "SayHello"
	toolDescription := "Says hello to someone"
	inputSchema := []byte(`{"type": "string"}`)
	tool, err := queries.CreateMCPTool(context.Background(), db.CreateMCPToolParams{
		ToolName:    toolName,
		Description: pgtype.Text{String: toolDescription, Valid: true},
		InputSchema: inputSchema,
	})
	require.NoError(t, err)

	// Insert a dummy tool mapping
	_, err = queries.CreateMCPToolMapping(context.Background(), db.CreateMCPToolMappingParams{
		McpToolID:     tool.ID,
		OpenapiPath:   "/hello",
		HttpMethod:    "POST",
		ParamMappings: []byte(`{}`),
		BodyMapping:   []byte(`{"strategy": "wrapped", "wrapped_property": "name"}`),
	})
	require.NoError(t, err)

	// Insert ACL for the tool in the profile
	_, err = queries.CreateProfileTool(context.Background(), db.CreateProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool.ID,
		Acl:       "EXECUTE",
	})
	require.NoError(t, err)

	// Start the MCP server in a goroutine
	port, err := GetFreePort()
	require.NoError(t, err)

	mcpServer := server.NewServer(queries)
	authHandler := server.AuthMiddleware(mcpServer)
	profileRouter := server.ProfileRouter(authHandler)

	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: profileRouter,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			t.Errorf("server error: %v", err)
		}
	}()

	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := srv.Shutdown(ctx); err != nil {
			t.Errorf("server shutdown error: %v", err)
		}
	}()

	// Wait for the server to be ready
	require.Eventually(t, func() bool {
		_, err := http.Get(fmt.Sprintf("http://localhost:%d/health", port))
		return err == nil
	}, 5*time.Second, 100*time.Millisecond, "server did not become ready")

	// Generate a JWT token for the dummy profile
	token, err := server.GenerateTestToken("test-user", profileName, port)
	require.NoError(t, err)

	// Setup mock HTTP server for API calls
	mockServer := SetupMockAPIServer(t)
	defer mockServer.Close()

	// Update the BaseURL of the MCP server to point to the mock API server
	mcpServer.BaseURL = mockServer.URL

	// --- Test tools/list ---
	listURL := fmt.Sprintf("http://localhost:%d/mcp/%s", port, profileName)
	listReqBody := `{"jsonrpc":"2.0","id":1,"method":"tools/list","params":{}}`
	listReq, err := http.NewRequest("POST", listURL, bytes.NewBufferString(listReqBody))
	require.NoError(t, err)
	listReq.Header.Set("Content-Type", "application/json")
	listReq.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	listResp, err := client.Do(listReq)
	require.NoError(t, err)
	defer listResp.Body.Close()

	assert.Equal(t, http.StatusOK, listResp.StatusCode)

	var listJsonResp server.JSONRPCResponse
	err = json.NewDecoder(listResp.Body).Decode(&listJsonResp)
	require.NoError(t, err)

	assert.Nil(t, listJsonResp.Error)
	assert.NotNil(t, listJsonResp.Result)

	listResultMap, ok := listJsonResp.Result.(map[string]interface{})
	assert.True(t, ok)
	tools, ok := listResultMap["tools"].([]interface{})
	assert.True(t, ok)
	assert.Len(t, tools, 1)
	toolMap := tools[0].(map[string]interface{})
	assert.Equal(t, toolName, toolMap["name"])

	// --- Test tools/call ---
	callURL := fmt.Sprintf("http://localhost:%d/mcp/%s", port, profileName)
	callReqBody := fmt.Sprintf(`{"jsonrpc":"2.0","id":2,"method":"tools/call","params":{"name":"%s","arguments":{"name":"World"}}}`, toolName)
	callReq, err := http.NewRequest("POST", callURL, bytes.NewBufferString(callReqBody))
	require.NoError(t, err)
	callReq.Header.Set("Content-Type", "application/json")
	callReq.Header.Set("Authorization", "Bearer "+token)

	callResp, err := client.Do(callReq)
	require.NoError(t, err)
	defer callResp.Body.Close()

	assert.Equal(t, http.StatusOK, callResp.StatusCode)

	var callJsonResp server.JSONRPCResponse
	err = json.NewDecoder(callResp.Body).Decode(&callJsonResp)
	require.NoError(t, err)

	assert.Nil(t, callJsonResp.Error)
	assert.NotNil(t, callJsonResp.Result)

	callResultMap, ok := callJsonResp.Result.(map[string]interface{})
	assert.True(t, ok)
	content, ok := callResultMap["content"].([]interface{})
	assert.True(t, ok)
	assert.Len(t, content, 1)
	contentMap := content[0].(map[string]interface{})
	assert.Equal(t, "text", contentMap["type"])
	assert.Contains(t, contentMap["text"], "Hello, World!")
}
