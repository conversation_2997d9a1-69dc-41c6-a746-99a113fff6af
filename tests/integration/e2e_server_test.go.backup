package integration

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"testing"
	"time"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/server"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestE2E_ServerInitialize(t *testing.T) {
	SkipIfNoDatabase(t)

	// Setup test database
	testDB := SetupTestDatabase(t)
	defer testDB.TearDown()

	testDB.RunMigrations(t)
	defer testDB.CleanupTables(t)

	// Insert a dummy profile
	conn, err := db.ConnectDB(testDB.ConnectionString)
	require.NoError(t, err)
	defer conn.Close(context.Background())

	queries := db.New(conn)
	profileName := "test-profile"
	_, err = queries.CreateProfile(context.Background(), db.CreateProfileParams{
		Name:       profileName,
		PathSegment: profileName,
	})
	require.NoError(t, err)

	// Start the MCP server in a goroutine
	port, err := GetFreePort()
	require.NoError(t, err)

	mcpServer := server.NewServer(queries)
	authHandler := server.AuthMiddleware(mcpServer)
	profileRouter := server.ProfileRouter(authHandler)

	srv := &http.Server{
		Addr:    fmt.Sprintf(":%d", port),
		Handler: profileRouter,
	}

	go func() {
		if err := srv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("server error: %v", err)
		}
	}()

	defer func() {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()
		if err := srv.Shutdown(ctx); err != nil {
			log.Fatalf("server shutdown error: %v", err)
		}
	}()

	// Wait for the server to be ready
	require.Eventually(t, func() bool {
		_, err := http.Get(fmt.Sprintf("http://localhost:%d/health", port))
		return err == nil
	}, 5*time.Second, 100*time.Millisecond, "server did not become ready")

	// Generate a JWT token for the dummy profile
	token, err := server.GenerateTestToken(profileName, profileName, port)
	require.NoError(t, err)

	// Make an HTTP request to the server's /mcp/{profile_name} endpoint with the JWT token
	url := fmt.Sprintf("http://localhost:%d/mcp/%s", port, profileName)
	reqBody := `{"jsonrpc":"2.0","id":1,"method":"initialize","params":{}}`
	req, err := http.NewRequest("POST", url, bytes.NewBufferString(reqBody))
	require.NoError(t, err)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Authorization", "Bearer "+token)

	client := &http.Client{}
	resp, err := client.Do(req)
	require.NoError(t, err)
	defer resp.Body.Close()

	assert.Equal(t, http.StatusOK, resp.StatusCode)

	var jsonResp server.JSONRPCResponse
	err = json.NewDecoder(resp.Body).Decode(&jsonResp)
	require.NoError(t, err)

	assert.Nil(t, jsonResp.Error)
	assert.NotNil(t, jsonResp.Result)

	resultMap, ok := jsonResp.Result.(map[string]interface{})
	assert.True(t, ok)
	assert.Equal(t, server.ProtocolVersion, resultMap["protocolVersion"])
	assert.Contains(t, resultMap, "capabilities")
	assert.Contains(t, resultMap, "serverInfo")
}
