package test_utils

import (
	"context"
	"flag"

	"github.com/ravan/suse-air/internal/db"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/stretchr/testify/mock"
	"github.com/urfave/cli/v2"
)

// MockQuerier is a mock implementation of db.Querier interface
type MockQuerier = MockDBQueries

// MockDBQueries is a mock implementation of db.Queries
type MockDBQueries struct {
	mock.Mock
}

func (m *MockDBQueries) CreateProfile(ctx context.Context, arg db.CreateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListProfiles(ctx context.Context) ([]db.Profile, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByName(ctx context.Context, name string) (db.Profile, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByID(ctx context.Context, id int64) (db.Profile, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) UpdateProfile(ctx context.Context, arg db.UpdateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) DeleteProfile(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) GetMCPToolByName(ctx context.Context, name string) (db.McpTool, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) GetMCPToolByID(ctx context.Context, id int64) (db.McpTool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) ListMCPToolsByProfile(ctx context.Context, profileName string) ([]db.McpTool, error) {
	args := m.Called(ctx, profileName)
	return args.Get(0).([]db.McpTool), args.Error(1)
}

func (m *MockDBQueries) CreateProfileTool(ctx context.Context, arg db.CreateProfileToolParams) (db.ProfileTool, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.ProfileTool), args.Error(1)
}

func (m *MockDBQueries) DeleteProfileTool(ctx context.Context, arg db.DeleteProfileToolParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) CreateUser(ctx context.Context, arg db.CreateUserParams) (db.User, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) ListUsers(ctx context.Context) ([]db.User, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.User), args.Error(1)
}

func (m *MockDBQueries) GetUserByUsername(ctx context.Context, username string) (db.User, error) {
	args := m.Called(ctx, username)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) CreateRole(ctx context.Context, name string) (db.Role, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) ListRoles(ctx context.Context) ([]db.Role, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Role), args.Error(1)
}

func (m *MockDBQueries) GetRoleByName(ctx context.Context, name string) (db.Role, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) GetRoleByID(ctx context.Context, id int64) (db.Role, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) UpdateRole(ctx context.Context, arg db.UpdateRoleParams) (db.Role, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) DeleteRole(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) CreateRoleProfile(ctx context.Context, arg db.CreateRoleProfileParams) (db.RoleProfile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.RoleProfile), args.Error(1)
}

func (m *MockDBQueries) DeleteRoleProfile(ctx context.Context, arg db.DeleteRoleProfileParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) ListProfilesByRole(ctx context.Context, roleID int64) ([]db.Profile, error) {
	args := m.Called(ctx, roleID)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListRolesByUser(ctx context.Context, userID int64) ([]db.Role, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]db.Role), args.Error(1)
}

func (m *MockDBQueries) UpdateUser(ctx context.Context, arg db.UpdateUserParams) (db.User, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) DeleteUser(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) CreateUserRole(ctx context.Context, arg db.CreateUserRoleParams) (db.UserRole, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.UserRole), args.Error(1)
}

func (m *MockDBQueries) DeleteUserRole(ctx context.Context, arg db.DeleteUserRoleParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

// MockDBConnection is a mock implementation of db.DBTX
type MockDBConnection struct {
	mock.Mock
}

func (m *MockDBConnection) Exec(ctx context.Context, sql string, arguments ...interface{}) (pgconn.CommandTag, error) {
	args := m.Called(ctx, sql, arguments)
	return args.Get(0).(pgconn.CommandTag), args.Error(1)
}

func (m *MockDBConnection) Query(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	mockArgs := m.Called(ctx, sql, args)
	return mockArgs.Get(0).(pgx.Rows), mockArgs.Error(1)
}

func (m *MockDBConnection) QueryRow(ctx context.Context, sql string, args ...interface{}) pgx.Row {
	mockArgs := m.Called(ctx, sql, args)
	return mockArgs.Get(0).(pgx.Row)
}

// MockDBQueries is a comprehensive mock implementation of db.Querier
type MockDBQueries struct {
	mock.Mock
}

// Profile methods
func (m *MockDBQueries) CreateProfile(ctx context.Context, arg db.CreateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListProfiles(ctx context.Context) ([]db.Profile, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByName(ctx context.Context, name string) (db.Profile, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByID(ctx context.Context, id int64) (db.Profile, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) UpdateProfile(ctx context.Context, arg db.UpdateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) DeleteProfile(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

// MCP Tool methods
func (m *MockDBQueries) GetMCPToolByName(ctx context.Context, toolName string) (db.McpTool, error) {
	args := m.Called(ctx, toolName)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) GetMCPToolByID(ctx context.Context, id int64) (db.McpTool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) ListMCPToolsByProfile(ctx context.Context, profileName string) ([]db.ListMCPToolsByProfileRow, error) {
	args := m.Called(ctx, profileName)
	return args.Get(0).([]db.ListMCPToolsByProfileRow), args.Error(1)
}

// Connection methods (for models tests)
func (m *MockDBQueries) CreateConnection(ctx context.Context, arg db.CreateConnectionParams) (db.Connection, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Connection), args.Error(1)
}

func (m *MockDBQueries) GetConnectionByName(ctx context.Context, name string) (db.Connection, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Connection), args.Error(1)
}

func (m *MockDBQueries) ListConnections(ctx context.Context) ([]db.Connection, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Connection), args.Error(1)
}

func (m *MockDBQueries) DeleteConnectionByName(ctx context.Context, name string) error {
	args := m.Called(ctx, name)
	return args.Error(0)
}

// Schema methods
func (m *MockDBQueries) CreateOpenAPISchema(ctx context.Context, arg db.CreateOpenAPISchemaParams) (db.OpenapiSchema, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.OpenapiSchema), args.Error(1)
}

func (m *MockDBQueries) GetOpenAPISchemaByName(ctx context.Context, name string) (db.OpenapiSchema, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.OpenapiSchema), args.Error(1)
}

func (m *MockDBQueries) GetSchemaConnection(ctx context.Context, arg db.GetSchemaConnectionParams) (db.SchemaConnection, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.SchemaConnection), args.Error(1)
}

func (m *MockDBQueries) CreateSchemaConnection(ctx context.Context, arg db.CreateSchemaConnectionParams) (db.SchemaConnection, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.SchemaConnection), args.Error(1)
}

// Add other methods as needed - this is a basic set for the failing tests

// Helper to create a cli.Context for testing
func CreateTestContext(args []string, dbQueries *MockDBQueries) *cli.Context {
	app := &cli.App{}

	// Mock db.ConnectDB to return a mock connection
	oldConnectDB := db.ConnectDB
	defer func() { db.ConnectDB = oldConnectDB }()
	db.ConnectDB = func(connString string) (db.DBTX, error) {
		mockConn := new(MockDBConnection)
		return mockConn, nil
	}

	set := flag.NewFlagSet("test", 0)
	_ = set.Parse(args)

	return cli.NewContext(app, set, nil)
}

// MockConnectDB is a function variable that can be mocked for testing
var MockConnectDB func(connectionString string) (db.Querier, error)
