package test_utils

import (
	"context"
	"flag"

	"github.com/ravan/suse-air/internal/db"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	"github.com/stretchr/testify/mock"
	"github.com/urfave/cli/v2"
)

// MockQuerier is a mock implementation of db.Querier interface
type MockQuerier = MockDBQueries

// MockDBQueries is a mock implementation of db.Queries
type MockDBQueries struct {
	mock.Mock
}

func (m *MockDBQueries) CreateProfile(ctx context.Context, arg db.CreateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListProfiles(ctx context.Context) ([]db.Profile, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByName(ctx context.Context, name string) (db.Profile, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) GetProfileByID(ctx context.Context, id int64) (db.Profile, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) UpdateProfile(ctx context.Context, arg db.UpdateProfileParams) (db.Profile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Profile), args.Error(1)
}

func (m *MockDBQueries) DeleteProfile(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) GetMCPToolByName(ctx context.Context, name string) (db.McpTool, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) GetMCPToolByID(ctx context.Context, id int64) (db.McpTool, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) ListMCPToolsByProfile(ctx context.Context, profileName string) ([]db.McpTool, error) {
	args := m.Called(ctx, profileName)
	return args.Get(0).([]db.McpTool), args.Error(1)
}

func (m *MockDBQueries) CreateProfileTool(ctx context.Context, arg db.CreateProfileToolParams) (db.ProfileTool, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.ProfileTool), args.Error(1)
}

func (m *MockDBQueries) DeleteProfileTool(ctx context.Context, arg db.DeleteProfileToolParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) CreateUser(ctx context.Context, arg db.CreateUserParams) (db.User, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) ListUsers(ctx context.Context) ([]db.User, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.User), args.Error(1)
}

func (m *MockDBQueries) GetUserByUsername(ctx context.Context, username string) (db.User, error) {
	args := m.Called(ctx, username)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) CreateRole(ctx context.Context, name string) (db.Role, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) ListRoles(ctx context.Context) ([]db.Role, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Role), args.Error(1)
}

func (m *MockDBQueries) GetRoleByName(ctx context.Context, name string) (db.Role, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) GetRoleByID(ctx context.Context, id int64) (db.Role, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) UpdateRole(ctx context.Context, arg db.UpdateRoleParams) (db.Role, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Role), args.Error(1)
}

func (m *MockDBQueries) DeleteRole(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) CreateRoleProfile(ctx context.Context, arg db.CreateRoleProfileParams) (db.RoleProfile, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.RoleProfile), args.Error(1)
}

func (m *MockDBQueries) DeleteRoleProfile(ctx context.Context, arg db.DeleteRoleProfileParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) ListProfilesByRole(ctx context.Context, roleID int64) ([]db.Profile, error) {
	args := m.Called(ctx, roleID)
	return args.Get(0).([]db.Profile), args.Error(1)
}

func (m *MockDBQueries) ListRolesByUser(ctx context.Context, userID int64) ([]db.Role, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]db.Role), args.Error(1)
}

func (m *MockDBQueries) UpdateUser(ctx context.Context, arg db.UpdateUserParams) (db.User, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.User), args.Error(1)
}

func (m *MockDBQueries) DeleteUser(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) CreateUserRole(ctx context.Context, arg db.CreateUserRoleParams) (db.UserRole, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.UserRole), args.Error(1)
}

func (m *MockDBQueries) DeleteUserRole(ctx context.Context, arg db.DeleteUserRoleParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

// MockDBConnection is a mock implementation of db.DBTX
type MockDBConnection struct {
	mock.Mock
}

func (m *MockDBConnection) Exec(ctx context.Context, sql string, arguments ...interface{}) (pgconn.CommandTag, error) {
	args := m.Called(ctx, sql, arguments)
	return args.Get(0).(pgconn.CommandTag), args.Error(1)
}

func (m *MockDBConnection) Query(ctx context.Context, sql string, args ...interface{}) (pgx.Rows, error) {
	mockArgs := m.Called(ctx, sql, args)
	return mockArgs.Get(0).(pgx.Rows), mockArgs.Error(1)
}

func (m *MockDBConnection) QueryRow(ctx context.Context, sql string, args ...interface{}) pgx.Row {
	mockArgs := m.Called(ctx, sql, args)
	return mockArgs.Get(0).(pgx.Row)
}

// Additional methods for comprehensive mock coverage (non-duplicates only)

// Connection methods (missing from original mock)
func (m *MockDBQueries) CreateConnection(ctx context.Context, arg db.CreateConnectionParams) (db.Connection, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Connection), args.Error(1)
}

func (m *MockDBQueries) GetConnectionByName(ctx context.Context, name string) (db.Connection, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.Connection), args.Error(1)
}

func (m *MockDBQueries) ListConnections(ctx context.Context) ([]db.Connection, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.Connection), args.Error(1)
}

func (m *MockDBQueries) DeleteConnectionByName(ctx context.Context, name string) error {
	args := m.Called(ctx, name)
	return args.Error(0)
}

// Schema methods (missing from original mock)
func (m *MockDBQueries) CreateOpenAPISchema(ctx context.Context, arg db.CreateOpenAPISchemaParams) (db.OpenapiSchema, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.OpenapiSchema), args.Error(1)
}

func (m *MockDBQueries) GetOpenAPISchemaByName(ctx context.Context, name string) (db.OpenapiSchema, error) {
	args := m.Called(ctx, name)
	return args.Get(0).(db.OpenapiSchema), args.Error(1)
}

func (m *MockDBQueries) GetSchemaConnection(ctx context.Context, arg db.GetSchemaConnectionParams) (db.SchemaConnection, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.SchemaConnection), args.Error(1)
}

func (m *MockDBQueries) CreateSchemaConnection(ctx context.Context, arg db.CreateSchemaConnectionParams) (db.SchemaConnection, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.SchemaConnection), args.Error(1)
}

// MCP Tool methods (missing from original mock)
func (m *MockDBQueries) CreateMCPTool(ctx context.Context, arg db.CreateMCPToolParams) (db.McpTool, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.McpTool), args.Error(1)
}

func (m *MockDBQueries) ListMCPTools(ctx context.Context) ([]db.McpTool, error) {
	args := m.Called(ctx)
	return args.Get(0).([]db.McpTool), args.Error(1)
}

func (m *MockDBQueries) CreateMCPToolMapping(ctx context.Context, arg db.CreateMCPToolMappingParams) (db.McpToolMapping, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.McpToolMapping), args.Error(1)
}

func (m *MockDBQueries) GetToolWithMapping(ctx context.Context, toolName string) (db.GetToolWithMappingRow, error) {
	args := m.Called(ctx, toolName)
	return args.Get(0).(db.GetToolWithMappingRow), args.Error(1)
}

func (m *MockDBQueries) GetToolWithConnectionInfo(ctx context.Context, toolName string) (db.GetToolWithConnectionInfoRow, error) {
	args := m.Called(ctx, toolName)
	return args.Get(0).(db.GetToolWithConnectionInfoRow), args.Error(1)
}

// OpenAPI Operation methods (missing from original mock)
func (m *MockDBQueries) CreateOpenAPIOperation(ctx context.Context, arg db.CreateOpenAPIOperationParams) (db.OpenapiOperation, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.OpenapiOperation), args.Error(1)
}

// Additional methods needed by models tests
func (m *MockDBQueries) UpdateConnection(ctx context.Context, arg db.UpdateConnectionParams) (db.Connection, error) {
	args := m.Called(ctx, arg)
	return args.Get(0).(db.Connection), args.Error(1)
}

func (m *MockDBQueries) DeleteConnection(ctx context.Context, id int64) error {
	args := m.Called(ctx, id)
	return args.Error(0)
}

func (m *MockDBQueries) GetConnectionByID(ctx context.Context, id int64) (db.Connection, error) {
	args := m.Called(ctx, id)
	return args.Get(0).(db.Connection), args.Error(1)
}

func (m *MockDBQueries) SetDefaultConnection(ctx context.Context, arg db.SetDefaultConnectionParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) DeleteSchemaConnection(ctx context.Context, arg db.DeleteSchemaConnectionParams) error {
	args := m.Called(ctx, arg)
	return args.Error(0)
}

func (m *MockDBQueries) ListConnectionsBySchema(ctx context.Context, schemaID int64) ([]db.Connection, error) {
	args := m.Called(ctx, schemaID)
	return args.Get(0).([]db.Connection), args.Error(1)
}

func (m *MockDBQueries) GetProfileByPathSegment(ctx context.Context, pathSegment string) (db.Profile, error) {
	args := m.Called(ctx, pathSegment)
	return args.Get(0).(db.Profile), args.Error(1)
}

// Add other methods as needed - this is a comprehensive set for the failing tests

// Helper to create a cli.Context for testing
func CreateTestContext(args []string, dbQueries *MockDBQueries) *cli.Context {
	app := &cli.App{}

	// Mock db.ConnectDB to return a mock connection
	oldConnectDB := db.ConnectDB
	defer func() { db.ConnectDB = oldConnectDB }()
	db.ConnectDB = func(connString string) (db.DBTX, error) {
		mockConn := new(MockDBConnection)
		return mockConn, nil
	}

	set := flag.NewFlagSet("test", 0)
	_ = set.Parse(args)

	return cli.NewContext(app, set, nil)
}

// MockConnectDB is a function variable that can be mocked for testing
var MockConnectDB func(connectionString string) (db.Querier, error)
