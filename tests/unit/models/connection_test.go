package models

import (
	"context"
	"testing"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/util"
	"github.com/ravan/suse-air/tests/unit/test_utils"
)

// Use shared MockDBQueries from test_utils

func TestConnectionService_CreateConnection(t *testing.T) {
	mockQuerier := new(test_utils.MockDBQueries)
	service := models.NewConnectionService(mockQuerier)
	ctx := context.Background()

	// Test data
	req := models.CreateConnectionRequest{
		Name:        "test-conn",
		Description: "Test connection",
		BaseURL:     "https://api.test.com",
		Auth: util.ConnectionAuth{
			Type:  "bearer",
			Token: "test-token",
		},
	}

	expectedConn := db.Connection{
		ID:          1,
		Name:        "test-conn",
		Description: pgtype.Text{String: "Test connection", Valid: true},
		BaseUrl:     "https://api.test.com",
		AuthType:    "bearer",
	}

	// Mock the database call
	mockQuerier.On("CreateConnection", ctx, mock.AnythingOfType("db.CreateConnectionParams")).Return(expectedConn, nil)

	// Execute
	result, err := service.CreateConnection(ctx, req)

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "test-conn", result.Name)
	assert.Equal(t, "Test connection", result.Description)
	assert.Equal(t, "https://api.test.com", result.BaseURL)
	assert.Equal(t, "bearer", result.Auth.Type)
	assert.Equal(t, "test-token", result.Auth.Token)

	mockQuerier.AssertExpectations(t)
}

func TestConnectionService_CreateConnection_InvalidAuthType(t *testing.T) {
	mockQuerier := new(test_utils.MockDBQueries)
	service := models.NewConnectionService(mockQuerier)
	ctx := context.Background()

	req := models.CreateConnectionRequest{
		Name:    "test-conn",
		BaseURL: "https://api.test.com",
		Auth: util.ConnectionAuth{
			Type: "invalid-auth-type",
		},
	}

	// Execute
	result, err := service.CreateConnection(ctx, req)

	// Assert
	assert.Error(t, err)
	assert.Nil(t, result)
	assert.Contains(t, err.Error(), "invalid auth type")

	mockQuerier.AssertExpectations(t)
}

func TestConnectionService_GetConnection(t *testing.T) {
	mockQuerier := new(test_utils.MockDBQueries)
	service := models.NewConnectionService(mockQuerier)
	ctx := context.Background()

	// Test data with encrypted token
	encKey := util.GetEncryptionKey()
	encryptedToken, _ := encKey.Encrypt("test-token")

	dbConn := db.Connection{
		ID:                 1,
		Name:               "test-conn",
		Description:        pgtype.Text{String: "Test connection", Valid: true},
		BaseUrl:            "https://api.test.com",
		AuthType:           "bearer",
		AuthTokenEncrypted: encryptedToken,
	}

	// Mock the database call
	mockQuerier.On("GetConnectionByName", ctx, "test-conn").Return(dbConn, nil)

	// Execute
	result, err := service.GetConnection(ctx, "test-conn")

	// Assert
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, int64(1), result.ID)
	assert.Equal(t, "test-conn", result.Name)
	assert.Equal(t, "Test connection", result.Description)
	assert.Equal(t, "https://api.test.com", result.BaseURL)
	assert.Equal(t, "bearer", result.Auth.Type)
	assert.Equal(t, "test-token", result.Auth.Token)

	mockQuerier.AssertExpectations(t)
}

func TestConnectionService_ListConnections(t *testing.T) {
	mockQuerier := new(test_utils.MockDBQueries)
	service := models.NewConnectionService(mockQuerier)
	ctx := context.Background()

	// Test data
	dbConns := []db.Connection{
		{
			ID:          1,
			Name:        "conn1",
			Description: pgtype.Text{String: "Connection 1", Valid: true},
			BaseUrl:     "https://api1.test.com",
			AuthType:    "bearer",
		},
		{
			ID:          2,
			Name:        "conn2",
			Description: pgtype.Text{String: "Connection 2", Valid: true},
			BaseUrl:     "https://api2.test.com",
			AuthType:    "basic",
			AuthUsername: pgtype.Text{String: "user", Valid: true},
		},
	}

	// Mock the database call
	mockQuerier.On("ListConnections", ctx).Return(dbConns, nil)

	// Execute
	result, err := service.ListConnections(ctx)

	// Assert
	assert.NoError(t, err)
	assert.Len(t, result, 2)
	assert.Equal(t, "conn1", result[0].Name)
	assert.Equal(t, "bearer", result[0].Auth.Type)
	assert.Equal(t, "conn2", result[1].Name)
	assert.Equal(t, "basic", result[1].Auth.Type)
	assert.Equal(t, "user", result[1].Auth.Username)
	// Sensitive data should not be included in list view
	assert.Empty(t, result[0].Auth.Token)
	assert.Empty(t, result[1].Auth.Password)

	mockQuerier.AssertExpectations(t)
}

func TestConnectionService_DeleteConnection(t *testing.T) {
	mockQuerier := new(test_utils.MockDBQueries)
	service := models.NewConnectionService(mockQuerier)
	ctx := context.Background()

	// Mock the database call
	mockQuerier.On("DeleteConnectionByName", ctx, "test-conn").Return(nil)

	// Execute
	err := service.DeleteConnection(ctx, "test-conn")

	// Assert
	assert.NoError(t, err)
	mockQuerier.AssertExpectations(t)
}

func TestSchemaService_AssociateConnection(t *testing.T) {
	mockQuerier := new(test_utils.MockDBQueries)
	service := models.NewSchemaService(mockQuerier)
	ctx := context.Background()

	// Test data
	schema := db.OpenapiSchema{ID: 1, Name: "test-schema"}
	conn := db.Connection{ID: 1, Name: "test-conn"}

	// Mock the database calls
	mockQuerier.On("GetOpenAPISchemaByName", ctx, "test-schema").Return(schema, nil)
	mockQuerier.On("GetConnectionByName", ctx, "test-conn").Return(conn, nil)
	mockQuerier.On("GetSchemaConnection", ctx, mock.AnythingOfType("db.GetSchemaConnectionParams")).Return(db.SchemaConnection{}, assert.AnError)
	mockQuerier.On("CreateSchemaConnection", ctx, mock.AnythingOfType("db.CreateSchemaConnectionParams")).Return(db.SchemaConnection{}, nil)

	// Execute
	err := service.AssociateConnection(ctx, "test-schema", "test-conn", true)

	// Assert
	assert.NoError(t, err)
	mockQuerier.AssertExpectations(t)
}

func TestSchemaService_DisassociateConnection(t *testing.T) {
	mockQuerier := new(test_utils.MockDBQueries)
	service := models.NewSchemaService(mockQuerier)
	ctx := context.Background()

	// Test data
	schema := db.OpenapiSchema{ID: 1, Name: "test-schema"}
	conn := db.Connection{ID: 1, Name: "test-conn"}

	// Mock the database calls
	mockQuerier.On("GetOpenAPISchemaByName", ctx, "test-schema").Return(schema, nil)
	mockQuerier.On("GetConnectionByName", ctx, "test-conn").Return(conn, nil)
	mockQuerier.On("DeleteSchemaConnection", ctx, mock.AnythingOfType("db.DeleteSchemaConnectionParams")).Return(nil)

	// Execute
	err := service.DisassociateConnection(ctx, "test-schema", "test-conn")

	// Assert
	assert.NoError(t, err)
	mockQuerier.AssertExpectations(t)
}
