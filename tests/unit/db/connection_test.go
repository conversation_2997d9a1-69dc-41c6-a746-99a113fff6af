package db

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/ravan/suse-air/internal/db"
)

func TestConnectDB_ValidConnection(t *testing.T) {
	// Note: This test requires a running PostgreSQL instance
	// In CI/CD environments, this might be skipped or use a test container
	connectionString := getTestConnectionString()
	if connectionString == "" {
		t.Skip("Skipping database connection test - no test database available")
	}

	conn, err := db.ConnectDB(connectionString)
	if err != nil {
		t.Skipf("Skipping test due to database connection error: %v", err)
	}
	// Note: DBTX interface doesn't have Close() or Ping() methods
	// The connection is successful if ConnectDB returns without error

	require.NoError(t, err)
	assert.NotNil(t, conn)

	// Test that we can create a queries object from the connection
	queries := db.New(conn)
	assert.NotNil(t, queries)
}

func TestConnectDB_InvalidConnectionString(t *testing.T) {
	invalidConnectionStrings := []string{
		"",
		"invalid-connection-string",
		"*******************************************/nonexistent",
		"postgres://user:password@localhost:99999/testdb", // Invalid port
	}

	for _, connStr := range invalidConnectionStrings {
		t.Run("Invalid: "+connStr, func(t *testing.T) {
			conn, err := db.ConnectDB(connStr)
			assert.Error(t, err)
			assert.Nil(t, conn)
		})
	}
}

func TestConnectDB_ErrorHandling(t *testing.T) {
	// Test with malformed URL
	_, err := db.ConnectDB("://malformed-url")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid connection string format")
}

func TestConnectDB_ConnectionStringVariations(t *testing.T) {
	// Test different valid connection string formats
	// Note: These won't actually connect, but should parse correctly
	validFormats := []string{
		"postgres://user:password@localhost:5432/dbname",
		"postgres://user:password@localhost/dbname",
		"postgres://user@localhost:5432/dbname",
		"postgresql://user:password@localhost:5432/dbname?sslmode=disable",
		"postgres://user:password@localhost:5432/dbname?sslmode=require&connect_timeout=10",
	}

	for _, connStr := range validFormats {
		t.Run("Format: "+connStr, func(t *testing.T) {
			// These will likely fail to connect to non-existent databases,
			// but the connection string parsing should work
			_, err := db.ConnectDB(connStr)
			// We expect an error, but it should be a connection error, not a parsing error
			if err != nil {
				// The error should indicate connection failure, not parsing failure
				// The actual error message will be database-specific
				assert.True(t, err != nil, "Expected connection error for invalid connection string")
			}
		})
	}
}

// getTestConnectionString returns a test database connection string
// This can be configured via environment variables or test configuration
func getTestConnectionString() string {
	// In a real test environment, you might use:
	// - Environment variables (TEST_DATABASE_URL)
	// - Docker containers (using dockertest)
	// - In-memory databases (if supported)
	// - Test configuration files
	
	// For now, return empty string to skip tests that require a real database
	// This can be overridden by setting TEST_DATABASE_URL environment variable
	// or by using a testing library like dockertest to spin up containers
	return ""
}

func TestConnectDB_ContextHandling(t *testing.T) {
	// Test that the function properly handles context for connection and ping
	connectionString := getTestConnectionString()
	if connectionString == "" {
		t.Skip("Skipping database connection test - no test database available")
	}

	conn, err := db.ConnectDB(connectionString)
	if err != nil {
		t.Skipf("Skipping test due to database connection error: %v", err)
	}
	// Note: DBTX interface doesn't have Close() or Ping() methods

	require.NoError(t, err)

	// Test that we can create a queries object from the connection
	queries := db.New(conn)
	assert.NotNil(t, queries)

	// Test that we can create multiple queries objects from the same connection
	queries2 := db.New(conn)
	assert.NotNil(t, queries2)
}

// Example of how you might structure integration tests with dockertest
// This is commented out since it requires additional setup

/*
func TestConnectDB_IntegrationWithDocker(t *testing.T) {
	// Using dockertest to spin up a PostgreSQL container for testing
	pool, err := dockertest.NewPool("")
	require.NoError(t, err)

	// Pull and start PostgreSQL container
	resource, err := pool.Run("postgres", "13", []string{
		"POSTGRES_PASSWORD=testpass",
		"POSTGRES_USER=testuser",
		"POSTGRES_DB=testdb",
	})
	require.NoError(t, err)

	// Cleanup
	defer func() {
		assert.NoError(t, pool.Purge(resource))
	}()

	// Build connection string
	connectionString := fmt.Sprintf(
		"postgres://testuser:testpass@localhost:%s/testdb?sslmode=disable",
		resource.GetPort("5432/tcp"),
	)

	// Wait for database to be ready
	err = pool.Retry(func() error {
		conn, err := db.ConnectDB(connectionString)
		if err != nil {
			return err
		}
		// Note: DBTX interface doesn't have Close() or Ping() methods
		// If ConnectDB succeeds, the database is ready
		return nil
	})
	require.NoError(t, err)

	// Now run actual tests
	conn, err := db.ConnectDB(connectionString)
	require.NoError(t, err)
	// Note: DBTX interface doesn't have Close() or Ping() methods

	assert.NotNil(t, conn)
	// Test that we can create queries from the connection
	queries := db.New(conn)
	assert.NotNil(t, queries)
}
*/