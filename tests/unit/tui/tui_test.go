package tui

import (
	"testing"

	"github.com/charmbracelet/bubbletea"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/tui"
	"github.com/ravan/suse-air/tests/unit/test_utils"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Use shared MockDBQueries from test_utils

// TestMainMenuNavigation tests basic main menu navigation
func TestMainMenuNavigation(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	// Set up basic mocks that might be called during initialization
	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{}, nil).Maybe()
	mockQueries.On("ListMCPTools", mock.Anything).Return([]db.McpTool{}, nil).Maybe()
	mockQueries.On("ListUsers", mock.Anything).Return([]db.User{}, nil).Maybe()
	mockQueries.On("ListRoles", mock.Anything).Return([]db.Role{}, nil).Maybe()
	mockQueries.On("ListConnections", mock.Anything).Return([]db.Connection{}, nil).Maybe()

	model := tui.InitialModelWithQueries(mockQueries)

	// Test that the model was created successfully
	assert.NotNil(t, model)

	// Test quit functionality
	updatedModel, cmd := model.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune("q")})
	assert.NotNil(t, updatedModel)
	assert.NotNil(t, cmd)
}

// TestProfileList tests listing profiles
func TestProfileList(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)
	expectedProfiles := []db.Profile{
		{
			ID:          1,
			Name:        "test-profile",
			Description: pgtype.Text{String: "Test profile", Valid: true},
			PathSegment: "test",
		},
	}

	// Set up mocks for initialization
	mockQueries.On("ListProfiles", mock.Anything).Return(expectedProfiles, nil)
	mockQueries.On("ListMCPTools", mock.Anything).Return([]db.McpTool{}, nil).Maybe()
	mockQueries.On("ListUsers", mock.Anything).Return([]db.User{}, nil).Maybe()
	mockQueries.On("ListRoles", mock.Anything).Return([]db.Role{}, nil).Maybe()
	mockQueries.On("ListConnections", mock.Anything).Return([]db.Connection{}, nil).Maybe()

	model := tui.InitialModelWithQueries(mockQueries)
	assert.NotNil(t, model)

	// Test that the model can be updated
	updatedModel, cmd := model.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune("esc")})
	assert.NotNil(t, updatedModel)
	assert.Nil(t, cmd) // esc should not return a command when already at main menu

	mockQueries.AssertExpectations(t)
}

// Additional test functions would go here...
// For brevity, I'm including just the essential tests

