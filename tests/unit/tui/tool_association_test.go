package tui

import (
	"testing"

	"github.com/charmbracelet/bubbletea"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/tui"
	"github.com/ravan/suse-air/tests/unit/test_utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Use shared MockDBQueries from test_utils

func TestToolAssociation_AddTool(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	// Mock data
	profile := db.Profile{
		ID:          1,
		Name:        "test-profile",
		Description: pgtype.Text{String: "Test profile", Valid: true},
		PathSegment: "test",
	}

	tool := db.McpTool{
		ID:          1,
		ToolName:    "test-tool",
		Description: pgtype.Text{String: "Test tool", Valid: true},
	}

	// Set up mocks for initialization
	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{profile}, nil)
	mockQueries.On("ListMCPTools", mock.Anything).Return([]db.McpTool{tool}, nil)
	mockQueries.On("ListMCPToolsByProfile", mock.Anything, "test-profile").Return([]db.ListMCPToolsByProfileRow{}, nil).Maybe()
	mockQueries.On("ListUsers", mock.Anything).Return([]db.User{}, nil).Maybe()
	mockQueries.On("ListRoles", mock.Anything).Return([]db.Role{}, nil).Maybe()
	mockQueries.On("ListConnections", mock.Anything).Return([]db.Connection{}, nil).Maybe()
	mockQueries.On("CreateProfileTool", mock.Anything, mock.AnythingOfType("db.CreateProfileToolParams")).Return(db.ProfileTool{
		ProfileID: 1,
		ToolID:    1,
		Acl:       "EXECUTE",
	}, nil).Maybe()

	// Initialize model
	model := tui.InitialModelWithQueries(mockQueries)
	assert.NotNil(t, model)

	// Test that the model can be updated (basic functionality test)
	updatedModel, cmd := model.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune("esc")})
	assert.NotNil(t, updatedModel)
	assert.Nil(t, cmd) // esc should not return a command when already at main menu

	mockQueries.AssertExpectations(t)
}

