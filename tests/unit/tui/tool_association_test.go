package tui

import (
	"context"
	"testing"

	"github.com/charmbracelet/bubbletea"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/tui"
	"github.com/ravan/suse-air/tests/unit/test_utils"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// Use shared MockDBQueries from test_utils

func TestToolAssociation_AddTool(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)
	
	// Mock data
	profile := db.Profile{
		ID:          1,
		Name:        "test-profile",
		Description: pgtype.Text{String: "Test profile", Valid: true},
		PathSegment: "test",
	}
	
	tool := db.McpTool{
		ID:          1,
		ToolName:    "test-tool",
		Description: pgtype.Text{String: "Test tool", Valid: true},
	}
	
	// Set up mocks
	mockQueries.On("ListProfiles", mock.Anything).Return([]db.Profile{profile}, nil)
	mockQueries.On("ListMCPTools", mock.Anything).Return([]db.McpTool{tool}, nil)
	mockQueries.On("ListMCPToolsByProfile", mock.Anything, "test-profile").Return([]db.ListMCPToolsByProfileRow{}, nil)
	mockQueries.On("CreateProfileTool", mock.Anything, mock.AnythingOfType("db.CreateProfileToolParams")).Return(db.ProfileTool{
		ProfileID: 1,
		ToolID:    1,
		Acl:       "EXECUTE",
	}, nil)
	
	// Initialize model
	m := tui.InitialModelWithQueries(mockQueries).(tui.Model)
	
	// Navigate to tool association
	m, _ = m.Update(tea.KeyMsg{Type: tea.KeyRunes, Runes: []rune("t")})
	assert.Equal(t, tui.ToolAssociation, m.CurrentView)
	
	mockQueries.AssertExpectations(t)
}

