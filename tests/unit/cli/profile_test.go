package cli

import (
	"bytes"
	"context"
	"flag"
	"fmt"
	"io"
	"os"
	"testing"
	"time"

	"github.com/ravan/suse-air/internal/cmd"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/tests/unit/test_utils"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/urfave/cli/v2"
)

// Helper to create a cli.Context for testing
func createTestContext(args []string, dbQueries *test_utils.MockDBQueries) *cli.Context {
	app := &cli.App{
		Commands: []*cli.Command{
			{
				Name: "profile",
				Subcommands: []*cli.Command{
					{
						Name: "create",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "description"},
							&cli.StringFlag{Name: "path-segment"},
							&cli.StringFlag{Name: "db"},
						},
						Action: func(c *cli.Context) error {
							// Note: We can't mock db.New directly since it's a function
							// Instead, we'll mock the database connection to return our mock queries
							// This is a limitation of the current test setup
							return cmd.RunProfileCreate(c)
						},
					},
					{
						Name: "list",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "db"},
						},
						Action: func(c *cli.Context) error {
							// Note: We can't mock db.New directly since it's a function
							// Instead, we'll mock the database connection to return our mock queries
							// This is a limitation of the current test setup
							return cmd.RunProfileList(c)
						},
					},
					{
						Name: "get",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "db"},
						},
						Action: func(c *cli.Context) error {
							// Note: Cannot mock db.New directly
							// This is a limitation of the current test setup
							// Would need to restructure to properly mock database layer
							return cmd.RunProfileGet(c)
						},
					},
					{
						Name: "update",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "new-name"},
							&cli.StringFlag{Name: "new-description"},
							&cli.StringFlag{Name: "new-path-segment"},
							&cli.StringFlag{Name: "db"},
						},
						Action: func(c *cli.Context) error {
							// Note: Cannot mock db.New directly
							// This is a limitation of the current test setup
							// Would need to restructure to properly mock database layer
							return cmd.RunProfileUpdate(c)
						},
					},
					{
						Name: "delete",
						Flags: []cli.Flag{
							&cli.StringFlag{Name: "name"},
							&cli.StringFlag{Name: "db"},
						},
						Action: func(c *cli.Context) error {
							// Note: Cannot mock db.New directly
							// This is a limitation of the current test setup
							// Would need to restructure to properly mock database layer
							return cmd.RunProfileDelete(c)
						},
					},
				},
			},
		},
	}

	// Mock db.ConnectDB to return a mock connection
	oldConnectDB := db.ConnectDB
	defer func() { db.ConnectDB = oldConnectDB }()
	db.ConnectDB = func(connString string) (db.DBTX, error) {
		mockConn := new(test_utils.MockDBConnection)
		return mockConn, nil
	}

	set := flag.NewFlagSet("test", 0)
	_ = set.Parse(args)

	return cli.NewContext(app, set, nil)
}

func TestRunProfileCreate(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)
	expectedProfile := db.Profile{
		ID:          1,
		Name:        "test-profile",
		Description: pgtype.Text{String: "A test profile", Valid: true},
		PathSegment: "test-path",
	}

	mockQueries.On("CreateProfile", mock.Anything, db.CreateProfileParams{
		Name:        "test-profile",
		Description: pgtype.Text{String: "A test profile", Valid: true},
		PathSegment: "test-path",
	}).Return(expectedProfile, nil).Once()

	// Capture stdout
	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := createTestContext([]string{"profile", "create", "--name", "test-profile", "--description", "A test profile", "--path-segment", "test-path"}, mockQueries)
	err := cmd.RunProfileCreate(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout // Restore stdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), "Profile created successfully: ID=1, Name=test-profile, PathSegment=test-path")
	mockQueries.AssertExpectations(t)
}

func TestRunProfileList(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)
	expectedProfiles := []db.Profile{
		{ID: 1, Name: "profile1", Description: pgtype.Text{String: "Desc1", Valid: true}, PathSegment: "path1"},
		{ID: 2, Name: "profile2", Description: pgtype.Text{String: "Desc2", Valid: true}, PathSegment: "path2"},
	}

	mockQueries.On("ListProfiles", mock.Anything).Return(expectedProfiles, nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := createTestContext([]string{"profile", "list"}, mockQueries)
	err := cmd.RunProfileList(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), "Profiles:")
	assert.Contains(t, string(out), "ID: 1, Name: profile1, Description: Desc1, PathSegment: path1")
	assert.Contains(t, string(out), "ID: 2, Name: profile2, Description: Desc2, PathSegment: path2")
	mockQueries.AssertExpectations(t)
}

func TestRunProfileGet(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)
	expectedProfile := db.Profile{
		ID:          1,
		Name:        "profile1",
		Description: pgtype.Text{String: "Desc1", Valid: true},
		PathSegment: "path1",
		CreatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
		UpdatedAt:   pgtype.Timestamptz{Time: time.Now(), Valid: true},
	}

	mockQueries.On("GetProfileByName", mock.Anything, "profile1").Return(expectedProfile, nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := createTestContext([]string{"profile", "get", "--name", "profile1"}, mockQueries)
	err := cmd.RunProfileGet(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), "Profile Details:")
	assert.Contains(t, string(out), "Name: profile1")
	assert.Contains(t, string(out), "Description: Desc1")
	mockQueries.AssertExpectations(t)
}

func TestRunProfileUpdate(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)
	oldProfile := db.Profile{
		ID:          1,
		Name:        "old-name",
		Description: pgtype.Text{String: "old-desc", Valid: true},
		PathSegment: "old-path",
	}
	updatedProfile := db.Profile{
		ID:          1,
		Name:        "new-name",
		Description: pgtype.Text{String: "new-desc", Valid: true},
		PathSegment: "new-path",
	}

	mockQueries.On("GetProfileByName", mock.Anything, "old-name").Return(oldProfile, nil).Once()
	mockQueries.On("UpdateProfile", mock.Anything, db.UpdateProfileParams{
		ID:          1,
		Name:        "new-name",
		Description: pgtype.Text{String: "new-desc", Valid: true},
		PathSegment: "new-path",
	}).Return(updatedProfile, nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := createTestContext([]string{"profile", "update", "--name", "old-name", "--new-name", "new-name", "--new-description", "new-desc", "--new-path-segment", "new-path"}, mockQueries)
	err := cmd.RunProfileUpdate(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), "Profile updated successfully: ID=1, Name=new-name, PathSegment=new-path")
	mockQueries.AssertExpectations(t)
}

func TestRunProfileDelete(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)
	existingProfile := db.Profile{
		ID:          1,
		Name:        "profile-to-delete",
		Description: pgtype.Text{String: "", Valid: false},
		PathSegment: "path-to-delete",
	}

	mockQueries.On("GetProfileByName", mock.Anything, "profile-to-delete").Return(existingProfile, nil).Once()
	mockQueries.On("DeleteProfile", mock.Anything, int64(1)).Return(nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := createTestContext([]string{"profile", "delete", "--name", "profile-to-delete"}, mockQueries)
	err := cmd.RunProfileDelete(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), "Profile profile-to-delete deleted successfully.")
	mockQueries.AssertExpectations(t)
