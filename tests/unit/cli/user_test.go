package cli

import (
	"flag"
	"bytes"
	"context"
	"fmt"
	"io"
	"os"
	"testing"

	"github.com/ravan/suse-air/internal/cmd"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/tests/unit/test_utils"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/urfave/cli/v2"
	"golang.org/x/crypto/bcrypt"
)

func TestRunUserCreate(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	username := "testuser"
	password := "password123"
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)

	expectedUser := db.User{
		ID:           1,
		Username:     username,
		PasswordHash: string(hashedPassword),
	}

	mockQueries.On("CreateUser", mock.Anything, db.CreateUserParams{
		Username:     username,
		PasswordHash: mock.AnythingOfType("string"), // We can't predict the hash, so we check type
	}).Return(expectedUser, nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := CreateTestContext([]string{"user", "create", "--username", username, "--password", password}, mockQueries)
	err := cmd.RunUserCreate(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), fmt.Sprintf("User created successfully: ID=1, Username=%s", username))
	mockQueries.AssertExpectations(t)
}

func TestRunUserList(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	expectedUsers := []db.User{
		{ID: 1, Username: "user1"},
		{ID: 2, Username: "user2"},
	}

	mockQueries.On("ListUsers", mock.Anything).Return(expectedUsers, nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := CreateTestContext([]string{"user", "list"}, mockQueries)
	err := cmd.RunUserList(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), "Users:")
	assert.Contains(t, string(out), "ID: 1, Username: user1")
	assert.Contains(t, string(out), "ID: 2, Username: user2")
	mockQueries.AssertExpectations(t)
}

func TestRunUserAssignRole(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	username := "testuser"
	roleName := "admin"

	user := db.User{ID: 1, Username: username}
	role := db.Role{ID: 10, Name: roleName}
	userRole := db.UserRole{UserID: user.ID, RoleID: role.ID}

	mockQueries.On("GetUserByUsername", mock.Anything, username).Return(user, nil).Once()
	mockQueries.On("GetRoleByName", mock.Anything, roleName).Return(role, nil).Once()
	mockQueries.On("CreateUserRole", mock.Anything, db.CreateUserRoleParams{
		UserID: user.ID,
		RoleID: role.ID,
	}).Return(userRole, nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := CreateTestContext([]string{"user", "assign-role", "--username", username, "--role", roleName}, mockQueries)
	err := cmd.RunUserAssignRole(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), fmt.Sprintf("Role %s assigned to user %s successfully.", roleName, username))
	mockQueries.AssertExpectations(t)
}

func TestRunUserRemoveRole(t *testing.T) {
	mockQueries := new(test_utils.MockDBQueries)

	username := "testuser"
	roleName := "admin"

	user := db.User{ID: 1, Username: username}
	role := db.Role{ID: 10, Name: roleName}

	mockQueries.On("GetUserByUsername", mock.Anything, username).Return(user, nil).Once()
	mockQueries.On("GetRoleByName", mock.Anything, roleName).Return(role, nil).Once()
	mockQueries.On("DeleteUserRole", mock.Anything, db.DeleteUserRoleParams{
		UserID: user.ID,
		RoleID: role.ID,
	}).Return(nil).Once()

	oldStdout := os.Stdout
	r, w, _ := os.Pipe()
	os.Stdout = w

	ctx := CreateTestContext([]string{"user", "remove-role", "--username", username, "--role", roleName}, mockQueries)
	err := cmd.RunUserRemoveRole(ctx)

	w.Close()
	out, _ := io.ReadAll(r)
	os.Stdout = oldStdout

	assert.NoError(t, err)
	assert.Contains(t, string(out), fmt.Sprintf("Role %s removed from user %s successfully.", roleName, username))
	mockQueries.AssertExpectations(t)
