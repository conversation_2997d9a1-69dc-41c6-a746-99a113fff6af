#!/bin/bash

# <PERSON>ript to convert PostgreSQL parameter syntax ($1, $2, etc.) to SQLite syntax (?, ?, etc.)
# in SQLite query files

SQLITE_DIR="sql/query_sqlite"

echo "Converting PostgreSQL parameter syntax to SQLite syntax..."

# Function to convert parameters in a file
convert_file() {
    local file="$1"
    echo "Converting $file..."
    
    # Create a backup
    cp "$file" "$file.backup"
    
    # Replace $1, $2, etc. with ? in order
    # This is a simplified conversion - in practice, you might need more sophisticated logic
    # to handle complex cases, but for our queries this should work
    
    # Use perl for more sophisticated regex replacement
    perl -i -pe '
        # Replace parameter references with placeholders
        s/\$\d+/\?/g;
    ' "$file"
}

# Convert all .sql files in the SQLite query directory
for file in "$SQLITE_DIR"/*.sql; do
    if [ -f "$file" ]; then
        convert_file "$file"
    fi
done

echo "Parameter conversion completed!"
echo "Backup files created with .backup extension"
