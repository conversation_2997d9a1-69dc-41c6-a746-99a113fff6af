-- Connection Management Queries (SQLite)

-- name: CreateConnection :one
-- Creates a new connection.
INSERT INTO connections (
    name, 
    description, 
    base_url, 
    auth_type, 
    auth_username, 
    auth_password_encrypted, 
    auth_token_encrypted, 
    auth_header_name, 
    auth_header_value_encrypted
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) 
RETURNING *;

-- name: GetConnectionByID :one
-- Retrieves a connection by its ID.
SELECT * FROM connections WHERE id = ? LIMIT 1;

-- name: GetConnectionByName :one
-- Retrieves a connection by its name.
SELECT * FROM connections WHERE name = ? LIMIT 1;

-- name: ListConnections :many
-- Lists all connections.
SELECT * FROM connections ORDER BY name;

-- name: UpdateConnection :one
-- Updates an existing connection.
UPDATE connections 
SET 
    name = COALESCE(?, name),
    description = COALESCE(?, description),
    base_url = COALESCE(?, base_url),
    auth_type = COALESCE(?, auth_type),
    auth_username = CO<PERSON><PERSON><PERSON>(?, auth_username),
    auth_password_encrypted = COALESCE(?, auth_password_encrypted),
    auth_token_encrypted = COALESCE(?, auth_token_encrypted),
    auth_header_name = COALESCE(?, auth_header_name),
    auth_header_value_encrypted = COALESCE(?, auth_header_value_encrypted),
    updated_at = CURRENT_TIMESTAMP
WHERE id = ? 
RETURNING *;

-- name: DeleteConnection :exec
-- Deletes a connection by its ID.
DELETE FROM connections WHERE id = ?;

-- name: DeleteConnectionByName :exec
-- Deletes a connection by its name.
DELETE FROM connections WHERE name = ?;

-- OpenAPI Schema Queries

-- name: CreateOpenAPISchema :one
-- Creates a new OpenAPI schema record.
INSERT INTO openapi_schemas (name, description, filename, version) 
VALUES (?, ?, ?, ?) 
RETURNING *;

-- name: GetOpenAPISchemaByID :one
-- Retrieves an OpenAPI schema by its ID.
SELECT * FROM openapi_schemas WHERE id = ? LIMIT 1;

-- name: GetOpenAPISchemaByName :one
-- Retrieves an OpenAPI schema by its name.
SELECT * FROM openapi_schemas WHERE name = ? LIMIT 1;

-- name: GetOpenAPISchemaByFilename :one
-- Retrieves an OpenAPI schema by its filename.
SELECT * FROM openapi_schemas WHERE filename = ? LIMIT 1;

-- name: ListOpenAPISchemas :many
-- Lists all OpenAPI schemas.
SELECT * FROM openapi_schemas ORDER BY name;

-- name: UpdateOpenAPISchema :one
-- Updates an existing OpenAPI schema.
UPDATE openapi_schemas 
SET 
    name = COALESCE(?, name),
    description = COALESCE(?, description),
    filename = COALESCE(?, filename),
    version = COALESCE(?, version),
    updated_at = CURRENT_TIMESTAMP
WHERE id = ? 
RETURNING *;

-- name: DeleteOpenAPISchema :exec
-- Deletes an OpenAPI schema by its ID.
DELETE FROM openapi_schemas WHERE id = ?;

-- OpenAPI Operation Queries

-- name: CreateOpenAPIOperation :one
-- Creates a new OpenAPI operation record.
INSERT INTO openapi_operations (schema_id, operation_id, path, method, summary, description) 
VALUES (?, ?, ?, ?, ?, ?) 
RETURNING *;

-- name: GetOpenAPIOperationByID :one
-- Retrieves an OpenAPI operation by its ID.
SELECT * FROM openapi_operations WHERE id = ? LIMIT 1;

-- name: GetOpenAPIOperationBySchemaAndOperationID :one
-- Retrieves an OpenAPI operation by schema ID and operation ID.
SELECT * FROM openapi_operations 
WHERE schema_id = ? AND operation_id = ? 
LIMIT 1;

-- name: ListOpenAPIOperationsBySchema :many
-- Lists all operations for a specific schema.
SELECT * FROM openapi_operations 
WHERE schema_id = ? 
ORDER BY path, method;

-- name: DeleteOpenAPIOperation :exec
-- Deletes an OpenAPI operation by its ID.
DELETE FROM openapi_operations WHERE id = ?;

-- Schema Connection Association Queries

-- name: CreateSchemaConnection :one
-- Associates a schema with a connection.
INSERT INTO schema_connections (schema_id, connection_id, is_default) 
VALUES (?, ?, ?) 
RETURNING *;

-- name: GetSchemaConnection :one
-- Gets a specific schema-connection association.
SELECT * FROM schema_connections 
WHERE schema_id = ? AND connection_id = ? 
LIMIT 1;

-- name: ListConnectionsBySchema :many
-- Lists all connections associated with a schema.
SELECT c.* FROM connections c
JOIN schema_connections sc ON c.id = sc.connection_id
WHERE sc.schema_id = ?
ORDER BY sc.is_default DESC, c.name;

-- name: ListSchemasByConnection :many
-- Lists all schemas associated with a connection.
SELECT s.* FROM openapi_schemas s
JOIN schema_connections sc ON s.id = sc.schema_id
WHERE sc.connection_id = ?
ORDER BY s.name;

-- name: GetDefaultConnectionForSchema :one
-- Gets the default connection for a schema.
SELECT c.* FROM connections c
JOIN schema_connections sc ON c.id = sc.connection_id
WHERE sc.schema_id = ? AND sc.is_default = 1
LIMIT 1;

-- name: SetDefaultConnection :exec
-- Sets a connection as the default for a schema (removes default from others).
UPDATE schema_connections 
SET is_default = CASE WHEN connection_id = ? THEN 1 ELSE 0 END
WHERE schema_id = ?;

-- name: DeleteSchemaConnection :exec
-- Removes a schema-connection association.
DELETE FROM schema_connections 
WHERE schema_id = ? AND connection_id = ?;

-- name: DeleteSchemaConnectionsBySchema :exec
-- Removes all connections for a schema.
DELETE FROM schema_connections WHERE schema_id = ?;

-- name: DeleteSchemaConnectionsByConnection :exec
-- Removes all schemas for a connection.
DELETE FROM schema_connections WHERE connection_id = ?;

-- Enhanced Tool Queries

-- name: GetToolWithConnectionInfo :one
-- Retrieves an MCP tool with its mapping and connection information.
SELECT 
    t.id as tool_id,
    t.tool_name,
    t.description as tool_description,
    t.input_schema,
    m.id as mapping_id,
    m.openapi_path,
    m.http_method,
    m.param_mappings,
    m.body_mapping,
    o.id as operation_id,
    o.operation_id as openapi_operation_id,
    s.id as schema_id,
    s.name as schema_name,
    s.filename as schema_filename,
    c.id as connection_id,
    c.name as connection_name,
    c.base_url,
    c.auth_type,
    c.auth_username,
    c.auth_password_encrypted,
    c.auth_token_encrypted,
    c.auth_header_name,
    c.auth_header_value_encrypted
FROM mcp_tools t
JOIN mcp_tool_mappings m ON t.id = m.mcp_tool_id
LEFT JOIN openapi_operations o ON m.operation_id = o.id
LEFT JOIN openapi_schemas s ON o.schema_id = s.id
LEFT JOIN schema_connections sc ON s.id = sc.schema_id AND sc.is_default = 1
LEFT JOIN connections c ON sc.connection_id = c.id
WHERE t.tool_name = ?;
