-- sql/query_sqlite/mcp_tool.sql
-- name: <PERSON>reateMCPTool :one
-- Inserts a new MCP tool into the database and returns it.
INSERT INTO mcp_tools (
    tool_name,
    description,
    input_schema
) VALUES (
             ?, ?, ?
         )
    RETURNING *;

-- name: CreateMCPToolMapping :one
-- Inserts a new mapping record for an MCP tool.
INSERT INTO mcp_tool_mappings (
    mcp_tool_id,
    openapi_path,
    http_method,
    param_mappings,
    body_mapping,
    operation_id
) VALUES (
             ?, ?, ?, ?, ?, ?
         )
    RETURNING *;


-- name: GetMCPToolByName :one
-- Retrieves a single MCP tool by its unique name.
SELECT * FROM mcp_tools
WHERE tool_name = ? LIMIT 1;

-- name: GetMCPToolByID :one
-- Retrieves a single MCP tool by its unique ID.
SELECT * FROM mcp_tools
WHERE id = ? LIMIT 1;

-- name: ListMCPTools :many
-- Lists all MCP tools stored in the database.
SELECT * FROM mcp_tools
ORDER BY tool_name;


-- name: GetToolWithMapping :one
-- Retrieves an MCP tool along with its corresponding mapping information.
SELECT
    t.id as tool_id,
    t.tool_name,
    t.description,
    t.input_schema,
    m.id as mapping_id,
    m.openapi_path,
    m.http_method,
    m.param_mappings,
    m.body_mapping
FROM
    mcp_tools t
        JOIN
    mcp_tool_mappings m ON t.id = m.mcp_tool_id
WHERE
    t.tool_name = ?;

-- name: ListMCPToolsByProfile :many
-- Lists MCP tools associated with a specific profile.
SELECT
    t.id, t.tool_name, t.description, t.input_schema, t.created_at, t.updated_at, pt.acl
FROM
    mcp_tools t
JOIN
    profile_tools pt ON t.id = pt.tool_id
JOIN
    profiles p ON pt.profile_id = p.id
WHERE
    p.name = ?
ORDER BY
    t.tool_name;

-- name: CreateProfile :one
-- Creates a new profile.
INSERT INTO profiles (name, description, path_segment) VALUES (?, ?, ?) RETURNING *;

-- name: GetProfileByName :one
-- Retrieves a profile by its name.
SELECT * FROM profiles WHERE name = ? LIMIT 1;

-- name: GetProfileByID :one
-- Retrieves a profile by its ID.
SELECT * FROM profiles WHERE id = ? LIMIT 1;

-- name: GetProfileByPathSegment :one
-- Retrieves a profile by its path segment.
SELECT * FROM profiles WHERE path_segment = ? LIMIT 1;

-- name: ListProfiles :many
-- Lists all profiles.
SELECT * FROM profiles ORDER BY name;

-- name: UpdateProfile :one
-- Updates an existing profile.
UPDATE profiles
SET
    name = ?,
    description = ?,
    path_segment = ?,
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = ?
RETURNING *;

-- name: DeleteProfile :exec
-- Deletes a profile by its ID.
DELETE FROM profiles WHERE id = ?;

-- name: CreateProfileTool :one
-- Links a profile to an MCP tool with a specific ACL.
INSERT INTO profile_tools (
    profile_id,
    tool_id,
    acl
) VALUES (
    ?, ?, ?
)
RETURNING *;

-- name: DeleteProfileTool :exec
-- Deletes a profile-tool association.
DELETE FROM profile_tools WHERE profile_id = ? AND tool_id = ?;

-- name: CreateUser :one
-- Creates a new user.
INSERT INTO users (username, password_hash) VALUES (?, ?) RETURNING *;

-- name: ListUsers :many
-- Lists all users.
SELECT * FROM users ORDER BY username;

-- name: GetUserByUsername :one
-- Retrieves a user by their username.
SELECT * FROM users WHERE username = ? LIMIT 1;

-- name: CreateRole :one
-- Creates a new role.
INSERT INTO roles (name) VALUES (?) RETURNING *;

-- name: GetRoleByName :one
-- Retrieves a role by its name.
SELECT * FROM roles WHERE name = ? LIMIT 1;

-- name: GetRoleByID :one
-- Retrieves a role by its ID.
SELECT * FROM roles WHERE id = ? LIMIT 1;

-- name: ListRoles :many
-- Lists all roles.
SELECT * FROM roles ORDER BY name;

-- name: UpdateRole :one
-- Updates an existing role.
UPDATE roles
SET
    name = ?,
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = ?
RETURNING *;

-- name: DeleteRole :exec
-- Deletes a role by its ID.
DELETE FROM roles WHERE id = ?;

-- name: CreateRoleProfile :one
-- Links a role to a profile.
INSERT INTO role_profiles (role_id, profile_id) VALUES (?, ?) RETURNING *;

-- name: DeleteRoleProfile :exec
-- Removes a role-profile association.
DELETE FROM role_profiles WHERE role_id = ? AND profile_id = ?;

-- name: ListProfilesByRole :many
-- Lists all profiles accessible by a role.
SELECT p.* FROM profiles p
JOIN role_profiles rp ON p.id = rp.profile_id
WHERE rp.role_id = ?
ORDER BY p.name;

-- name: ListRolesByUser :many
-- Lists all roles assigned to a user.
SELECT r.* FROM roles r
JOIN user_roles ur ON r.id = ur.role_id
WHERE ur.user_id = ?
ORDER BY r.name;

-- name: CreateUserRole :one
-- Assigns a role to a user.
INSERT INTO user_roles (user_id, role_id) VALUES (?, ?) RETURNING *;

-- name: DeleteUserRole :exec
-- Removes a role from a user.
DELETE FROM user_roles WHERE user_id = ? AND role_id = ?;

-- name: UpdateUser :one
-- Updates an existing user.
UPDATE users
SET
    password_hash = ?,
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = ?
RETURNING *;

-- name: DeleteUser :exec
-- Deletes a user by its ID.
DELETE FROM users WHERE id = ?;
