-- SUSE AIR: Connection Management & Execution Context (SQLite)
-- Phase 5: Add tables for managing connections to backend REST services

-- +goose Up

-- Enable foreign key constraints in SQLite
PRAGMA foreign_keys = ON;

-- Connections table to store backend service connection details
CREATE TABLE connections (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    base_url TEXT NOT NULL,
    auth_type TEXT NOT NULL CHECK (auth_type IN ('none', 'basic', 'bearer', 'custom_header')),
    auth_username TEXT, -- For basic auth
    auth_password_encrypted BLOB, -- Encrypted password for basic auth
    auth_token_encrypted BLOB, -- Encrypted token for bearer auth
    auth_header_name TEXT, -- For custom header auth
    auth_header_value_encrypted BLOB, -- Encrypted header value for custom header auth
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_connections_name ON connections (name);

-- <PERSON><PERSON><PERSON> schemas table to store references to original schema files
CREATE TABLE openapi_schemas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL UNIQUE,
    description TEXT,
    filename TEXT NOT NULL,
    version TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_openapi_schemas_name ON openapi_schemas (name);
CREATE INDEX idx_openapi_schemas_filename ON openapi_schemas (filename);

-- OpenAPI operations table to link operations to schemas
CREATE TABLE openapi_operations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    schema_id INTEGER NOT NULL REFERENCES openapi_schemas(id) ON DELETE CASCADE,
    operation_id TEXT NOT NULL,
    path TEXT NOT NULL,
    method TEXT NOT NULL,
    summary TEXT,
    description TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE UNIQUE INDEX idx_openapi_operations_schema_operation ON openapi_operations (schema_id, operation_id);
CREATE INDEX idx_openapi_operations_schema_id ON openapi_operations (schema_id);

-- Schema connections join table for many-to-many relationship
CREATE TABLE schema_connections (
    schema_id INTEGER NOT NULL REFERENCES openapi_schemas(id) ON DELETE CASCADE,
    connection_id INTEGER NOT NULL REFERENCES connections(id) ON DELETE CASCADE,
    is_default INTEGER NOT NULL DEFAULT 0, -- SQLite uses INTEGER for boolean (0=false, 1=true)
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (schema_id, connection_id)
);

CREATE INDEX idx_schema_connections_schema_id ON schema_connections (schema_id);
CREATE INDEX idx_schema_connections_connection_id ON schema_connections (connection_id);
CREATE INDEX idx_schema_connections_default ON schema_connections (schema_id, is_default) WHERE is_default = 1;

-- Add foreign key to mcp_tool_mappings to link to openapi_operations
ALTER TABLE mcp_tool_mappings 
ADD COLUMN operation_id INTEGER REFERENCES openapi_operations(id) ON DELETE SET NULL;

CREATE INDEX idx_mcp_tool_mappings_operation_id ON mcp_tool_mappings (operation_id);

-- +goose Down

-- Remove the foreign key from mcp_tool_mappings
-- Note: SQLite doesn't support DROP COLUMN directly, so we need to recreate the table
-- For simplicity in this migration, we'll leave the column but drop the index
DROP INDEX IF EXISTS idx_mcp_tool_mappings_operation_id;

-- Drop tables in reverse order to handle foreign key constraints
DROP TABLE IF EXISTS schema_connections;
DROP TABLE IF EXISTS openapi_operations;
DROP TABLE IF EXISTS openapi_schemas;
DROP TABLE IF EXISTS connections;
