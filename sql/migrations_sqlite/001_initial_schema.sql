-- SUSE AIR: Consolidated Database Schema (SQLite)
-- This is a single, valid Goose migration file for SQLite.

-- +goose Up
-- Create all tables in an order that respects foreign key constraints.

-- Enable foreign key constraints in SQLite
PRAGMA foreign_keys = ON;

-- Core tool definition tables
CREATE TABLE mcp_tools (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    tool_name TEXT NOT NULL,
    description TEXT,
    input_schema TEXT NOT NULL, -- <PERSON><PERSON><PERSON> stored as TEXT in SQLite
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE UNIQUE INDEX idx_tool_name ON mcp_tools (tool_name);

CREATE TABLE mcp_tool_mappings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    mcp_tool_id INTEGER NOT NULL REFERENCES mcp_tools(id) ON DELETE CASCADE,
    openapi_path TEXT NOT NULL,
    http_method TEXT NOT NULL,
    param_mappings TEXT, -- <PERSON><PERSON><PERSON> stored as TEXT in SQLite
    body_mapping TEXT, -- <PERSON><PERSON><PERSON> stored as TEXT in SQLite
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE UNIQUE INDEX idx_mcp_tool_id ON mcp_tool_mappings (mcp_tool_id);

-- Profile and Role definition tables
CREATE TABLE profiles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    description TEXT,
    path_segment TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE UNIQUE INDEX idx_profile_name ON profiles (name);
CREATE UNIQUE INDEX idx_profile_path_segment ON profiles (path_segment);

CREATE TABLE roles (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE UNIQUE INDEX idx_role_name ON roles (name);

-- User table
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL UNIQUE,
    password_hash TEXT NOT NULL,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP
);

-- Join tables for relationships
CREATE TABLE profile_tools (
    profile_id INTEGER NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    tool_id INTEGER NOT NULL REFERENCES mcp_tools(id) ON DELETE CASCADE,
    acl TEXT NOT NULL, -- e.g., 'EXECUTE', 'READ_ONLY', 'DENY'
    PRIMARY KEY (profile_id, tool_id)
);

CREATE TABLE role_profiles (
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    profile_id INTEGER NOT NULL REFERENCES profiles(id) ON DELETE CASCADE,
    PRIMARY KEY (role_id, profile_id)
);

CREATE TABLE user_roles (
    user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES roles(id) ON DELETE CASCADE,
    PRIMARY KEY (user_id, role_id)
);

-- +goose Down
-- Drop all tables in the reverse order of creation to avoid foreign key errors.
DROP TABLE IF EXISTS user_roles;
DROP TABLE IF EXISTS role_profiles;
DROP TABLE IF EXISTS profile_tools;
DROP TABLE IF EXISTS users;
DROP TABLE IF EXISTS roles;
DROP TABLE IF EXISTS profiles;
DROP TABLE IF EXISTS mcp_tool_mappings;
DROP TABLE IF EXISTS mcp_tools;
