#!/bin/bash

# Script to replace <PERSON><PERSON><PERSON><PERSON><PERSON> with test_utils.MockDBQueries

echo "Replacing <PERSON><PERSON>Quer<PERSON> with test_utils.MockDBQueries..."

# Update models test files
for file in tests/unit/models/*.go; do
    if [ -f "$file" ]; then
        echo "Updating $file..."
        sed -i '' 's/MockQuerier/test_utils.MockDBQueries/g' "$file"
        sed -i '' 's/new(test_utils.MockDBQueries)/new(test_utils.MockDBQueries)/g' "$file"
    fi
done

echo "MockQuerier references updated!"
