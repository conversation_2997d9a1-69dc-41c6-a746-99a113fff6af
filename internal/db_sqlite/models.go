// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db_sqlite

import (
	"database/sql"
	"time"
)

type Connection struct {
	ID                       int64          `json:"id"`
	Name                     string         `json:"name"`
	Description              sql.NullString `json:"description"`
	BaseUrl                  string         `json:"base_url"`
	AuthType                 string         `json:"auth_type"`
	AuthUsername             sql.NullString `json:"auth_username"`
	AuthPasswordEncrypted    []byte         `json:"auth_password_encrypted"`
	AuthTokenEncrypted       []byte         `json:"auth_token_encrypted"`
	AuthHeaderName           sql.NullString `json:"auth_header_name"`
	AuthHeaderValueEncrypted []byte         `json:"auth_header_value_encrypted"`
	CreatedAt                time.Time      `json:"created_at"`
	UpdatedAt                time.Time      `json:"updated_at"`
}

type McpTool struct {
	ID          int64          `json:"id"`
	ToolName    string         `json:"tool_name"`
	Description sql.NullString `json:"description"`
	InputSchema string         `json:"input_schema"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

type McpToolMapping struct {
	ID            int64          `json:"id"`
	McpToolID     int64          `json:"mcp_tool_id"`
	OpenapiPath   string         `json:"openapi_path"`
	HttpMethod    string         `json:"http_method"`
	ParamMappings sql.NullString `json:"param_mappings"`
	BodyMapping   sql.NullString `json:"body_mapping"`
	CreatedAt     time.Time      `json:"created_at"`
	OperationID   sql.NullInt64  `json:"operation_id"`
}

type OpenapiOperation struct {
	ID          int64          `json:"id"`
	SchemaID    int64          `json:"schema_id"`
	OperationID string         `json:"operation_id"`
	Path        string         `json:"path"`
	Method      string         `json:"method"`
	Summary     sql.NullString `json:"summary"`
	Description sql.NullString `json:"description"`
	CreatedAt   time.Time      `json:"created_at"`
}

type OpenapiSchema struct {
	ID          int64          `json:"id"`
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
	Filename    string         `json:"filename"`
	Version     sql.NullString `json:"version"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

type Profile struct {
	ID          int64          `json:"id"`
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
	PathSegment string         `json:"path_segment"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
}

type ProfileTool struct {
	ProfileID int64  `json:"profile_id"`
	ToolID    int64  `json:"tool_id"`
	Acl       string `json:"acl"`
}

type Role struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

type RoleProfile struct {
	RoleID    int64 `json:"role_id"`
	ProfileID int64 `json:"profile_id"`
}

type SchemaConnection struct {
	SchemaID     int64     `json:"schema_id"`
	ConnectionID int64     `json:"connection_id"`
	IsDefault    int64     `json:"is_default"`
	CreatedAt    time.Time `json:"created_at"`
}

type User struct {
	ID           int64     `json:"id"`
	Username     string    `json:"username"`
	PasswordHash string    `json:"password_hash"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

type UserRole struct {
	UserID int64 `json:"user_id"`
	RoleID int64 `json:"role_id"`
}
