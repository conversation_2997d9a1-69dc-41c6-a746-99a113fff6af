// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package db_sqlite

import (
	"context"
)

type Querier interface {
	// Connection Management Queries (SQLite)
	// Creates a new connection.
	CreateConnection(ctx context.Context, arg CreateConnectionParams) (Connection, error)
	// sql/query_sqlite/mcp_tool.sql
	// Inserts a new MCP tool into the database and returns it.
	CreateMCPTool(ctx context.Context, arg CreateMCPToolParams) (McpTool, error)
	// Inserts a new mapping record for an MCP tool.
	CreateMCPToolMapping(ctx context.Context, arg CreateMCPToolMappingParams) (McpToolMapping, error)
	// OpenAPI Operation Queries
	// Creates a new OpenAPI operation record.
	CreateOpenAPIOperation(ctx context.Context, arg CreateOpenAPIOperationParams) (OpenapiOperation, error)
	// OpenAPI Schema Queries
	// Creates a new OpenAPI schema record.
	CreateOpenAPISchema(ctx context.Context, arg CreateOpenAPISchemaParams) (OpenapiSchema, error)
	// Creates a new profile.
	CreateProfile(ctx context.Context, arg CreateProfileParams) (Profile, error)
	// Links a profile to an MCP tool with a specific ACL.
	CreateProfileTool(ctx context.Context, arg CreateProfileToolParams) (ProfileTool, error)
	// Creates a new role.
	CreateRole(ctx context.Context, name string) (Role, error)
	// Links a role to a profile.
	CreateRoleProfile(ctx context.Context, arg CreateRoleProfileParams) (RoleProfile, error)
	// Schema Connection Association Queries
	// Associates a schema with a connection.
	CreateSchemaConnection(ctx context.Context, arg CreateSchemaConnectionParams) (SchemaConnection, error)
	// Creates a new user.
	CreateUser(ctx context.Context, arg CreateUserParams) (User, error)
	// Assigns a role to a user.
	CreateUserRole(ctx context.Context, arg CreateUserRoleParams) (UserRole, error)
	// Deletes a connection by its ID.
	DeleteConnection(ctx context.Context, id int64) error
	// Deletes a connection by its name.
	DeleteConnectionByName(ctx context.Context, name string) error
	// Deletes an OpenAPI operation by its ID.
	DeleteOpenAPIOperation(ctx context.Context, id int64) error
	// Deletes an OpenAPI schema by its ID.
	DeleteOpenAPISchema(ctx context.Context, id int64) error
	// Deletes a profile by its ID.
	DeleteProfile(ctx context.Context, id int64) error
	// Deletes a profile-tool association.
	DeleteProfileTool(ctx context.Context, arg DeleteProfileToolParams) error
	// Deletes a role by its ID.
	DeleteRole(ctx context.Context, id int64) error
	// Removes a role-profile association.
	DeleteRoleProfile(ctx context.Context, arg DeleteRoleProfileParams) error
	// Removes a schema-connection association.
	DeleteSchemaConnection(ctx context.Context, arg DeleteSchemaConnectionParams) error
	// Removes all schemas for a connection.
	DeleteSchemaConnectionsByConnection(ctx context.Context, connectionID int64) error
	// Removes all connections for a schema.
	DeleteSchemaConnectionsBySchema(ctx context.Context, schemaID int64) error
	// Deletes a user by its ID.
	DeleteUser(ctx context.Context, id int64) error
	// Removes a role from a user.
	DeleteUserRole(ctx context.Context, arg DeleteUserRoleParams) error
	// Retrieves a connection by its ID.
	GetConnectionByID(ctx context.Context, id int64) (Connection, error)
	// Retrieves a connection by its name.
	GetConnectionByName(ctx context.Context, name string) (Connection, error)
	// Gets the default connection for a schema.
	GetDefaultConnectionForSchema(ctx context.Context, schemaID int64) (Connection, error)
	// Retrieves a single MCP tool by its unique ID.
	GetMCPToolByID(ctx context.Context, id int64) (McpTool, error)
	// Retrieves a single MCP tool by its unique name.
	GetMCPToolByName(ctx context.Context, toolName string) (McpTool, error)
	// Retrieves an OpenAPI operation by its ID.
	GetOpenAPIOperationByID(ctx context.Context, id int64) (OpenapiOperation, error)
	// Retrieves an OpenAPI operation by schema ID and operation ID.
	GetOpenAPIOperationBySchemaAndOperationID(ctx context.Context, arg GetOpenAPIOperationBySchemaAndOperationIDParams) (OpenapiOperation, error)
	// Retrieves an OpenAPI schema by its filename.
	GetOpenAPISchemaByFilename(ctx context.Context, filename string) (OpenapiSchema, error)
	// Retrieves an OpenAPI schema by its ID.
	GetOpenAPISchemaByID(ctx context.Context, id int64) (OpenapiSchema, error)
	// Retrieves an OpenAPI schema by its name.
	GetOpenAPISchemaByName(ctx context.Context, name string) (OpenapiSchema, error)
	// Retrieves a profile by its ID.
	GetProfileByID(ctx context.Context, id int64) (Profile, error)
	// Retrieves a profile by its name.
	GetProfileByName(ctx context.Context, name string) (Profile, error)
	// Retrieves a profile by its path segment.
	GetProfileByPathSegment(ctx context.Context, pathSegment string) (Profile, error)
	// Retrieves a role by its ID.
	GetRoleByID(ctx context.Context, id int64) (Role, error)
	// Retrieves a role by its name.
	GetRoleByName(ctx context.Context, name string) (Role, error)
	// Gets a specific schema-connection association.
	GetSchemaConnection(ctx context.Context, arg GetSchemaConnectionParams) (SchemaConnection, error)
	// Enhanced Tool Queries
	// Retrieves an MCP tool with its mapping and connection information.
	GetToolWithConnectionInfo(ctx context.Context, toolName string) (GetToolWithConnectionInfoRow, error)
	// Retrieves an MCP tool along with its corresponding mapping information.
	GetToolWithMapping(ctx context.Context, toolName string) (GetToolWithMappingRow, error)
	// Retrieves a user by their username.
	GetUserByUsername(ctx context.Context, username string) (User, error)
	// Lists all connections.
	ListConnections(ctx context.Context) ([]Connection, error)
	// Lists all connections associated with a schema.
	ListConnectionsBySchema(ctx context.Context, schemaID int64) ([]Connection, error)
	// Lists all MCP tools stored in the database.
	ListMCPTools(ctx context.Context) ([]McpTool, error)
	// Lists MCP tools associated with a specific profile.
	ListMCPToolsByProfile(ctx context.Context, name string) ([]ListMCPToolsByProfileRow, error)
	// Lists all operations for a specific schema.
	ListOpenAPIOperationsBySchema(ctx context.Context, schemaID int64) ([]OpenapiOperation, error)
	// Lists all OpenAPI schemas.
	ListOpenAPISchemas(ctx context.Context) ([]OpenapiSchema, error)
	// Lists all profiles.
	ListProfiles(ctx context.Context) ([]Profile, error)
	// Lists all profiles accessible by a role.
	ListProfilesByRole(ctx context.Context, roleID int64) ([]Profile, error)
	// Lists all roles.
	ListRoles(ctx context.Context) ([]Role, error)
	// Lists all roles assigned to a user.
	ListRolesByUser(ctx context.Context, userID int64) ([]Role, error)
	// Lists all schemas associated with a connection.
	ListSchemasByConnection(ctx context.Context, connectionID int64) ([]OpenapiSchema, error)
	// Lists all users.
	ListUsers(ctx context.Context) ([]User, error)
	// Sets a connection as the default for a schema (removes default from others).
	SetDefaultConnection(ctx context.Context, arg SetDefaultConnectionParams) error
	// Updates an existing connection.
	UpdateConnection(ctx context.Context, arg UpdateConnectionParams) (Connection, error)
	// Updates an existing OpenAPI schema.
	UpdateOpenAPISchema(ctx context.Context, arg UpdateOpenAPISchemaParams) (OpenapiSchema, error)
	// Updates an existing profile.
	UpdateProfile(ctx context.Context, arg UpdateProfileParams) (Profile, error)
	// Updates an existing role.
	UpdateRole(ctx context.Context, arg UpdateRoleParams) (Role, error)
	// Updates an existing user.
	UpdateUser(ctx context.Context, arg UpdateUserParams) (User, error)
}

var _ Querier = (*Queries)(nil)
