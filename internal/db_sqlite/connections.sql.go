// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: connections.sql

package db_sqlite

import (
	"context"
	"database/sql"
)

const createConnection = `-- name: CreateConnection :one

INSERT INTO connections (
    name, 
    description, 
    base_url, 
    auth_type, 
    auth_username, 
    auth_password_encrypted, 
    auth_token_encrypted, 
    auth_header_name, 
    auth_header_value_encrypted
) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?) 
RETURNING id, name, description, base_url, auth_type, auth_username, auth_password_encrypted, auth_token_encrypted, auth_header_name, auth_header_value_encrypted, created_at, updated_at
`

type CreateConnectionParams struct {
	Name                     string         `json:"name"`
	Description              sql.NullString `json:"description"`
	BaseUrl                  string         `json:"base_url"`
	AuthType                 string         `json:"auth_type"`
	AuthUsername             sql.NullString `json:"auth_username"`
	AuthPasswordEncrypted    []byte         `json:"auth_password_encrypted"`
	AuthTokenEncrypted       []byte         `json:"auth_token_encrypted"`
	AuthHeaderName           sql.NullString `json:"auth_header_name"`
	AuthHeaderValueEncrypted []byte         `json:"auth_header_value_encrypted"`
}

// Connection Management Queries (SQLite)
// Creates a new connection.
func (q *Queries) CreateConnection(ctx context.Context, arg CreateConnectionParams) (Connection, error) {
	row := q.db.QueryRowContext(ctx, createConnection,
		arg.Name,
		arg.Description,
		arg.BaseUrl,
		arg.AuthType,
		arg.AuthUsername,
		arg.AuthPasswordEncrypted,
		arg.AuthTokenEncrypted,
		arg.AuthHeaderName,
		arg.AuthHeaderValueEncrypted,
	)
	var i Connection
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.BaseUrl,
		&i.AuthType,
		&i.AuthUsername,
		&i.AuthPasswordEncrypted,
		&i.AuthTokenEncrypted,
		&i.AuthHeaderName,
		&i.AuthHeaderValueEncrypted,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createOpenAPIOperation = `-- name: CreateOpenAPIOperation :one

INSERT INTO openapi_operations (schema_id, operation_id, path, method, summary, description) 
VALUES (?, ?, ?, ?, ?, ?) 
RETURNING id, schema_id, operation_id, path, method, summary, description, created_at
`

type CreateOpenAPIOperationParams struct {
	SchemaID    int64          `json:"schema_id"`
	OperationID string         `json:"operation_id"`
	Path        string         `json:"path"`
	Method      string         `json:"method"`
	Summary     sql.NullString `json:"summary"`
	Description sql.NullString `json:"description"`
}

// OpenAPI Operation Queries
// Creates a new OpenAPI operation record.
func (q *Queries) CreateOpenAPIOperation(ctx context.Context, arg CreateOpenAPIOperationParams) (OpenapiOperation, error) {
	row := q.db.QueryRowContext(ctx, createOpenAPIOperation,
		arg.SchemaID,
		arg.OperationID,
		arg.Path,
		arg.Method,
		arg.Summary,
		arg.Description,
	)
	var i OpenapiOperation
	err := row.Scan(
		&i.ID,
		&i.SchemaID,
		&i.OperationID,
		&i.Path,
		&i.Method,
		&i.Summary,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const createOpenAPISchema = `-- name: CreateOpenAPISchema :one

INSERT INTO openapi_schemas (name, description, filename, version) 
VALUES (?, ?, ?, ?) 
RETURNING id, name, description, filename, version, created_at, updated_at
`

type CreateOpenAPISchemaParams struct {
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
	Filename    string         `json:"filename"`
	Version     sql.NullString `json:"version"`
}

// OpenAPI Schema Queries
// Creates a new OpenAPI schema record.
func (q *Queries) CreateOpenAPISchema(ctx context.Context, arg CreateOpenAPISchemaParams) (OpenapiSchema, error) {
	row := q.db.QueryRowContext(ctx, createOpenAPISchema,
		arg.Name,
		arg.Description,
		arg.Filename,
		arg.Version,
	)
	var i OpenapiSchema
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Filename,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const createSchemaConnection = `-- name: CreateSchemaConnection :one

INSERT INTO schema_connections (schema_id, connection_id, is_default) 
VALUES (?, ?, ?) 
RETURNING schema_id, connection_id, is_default, created_at
`

type CreateSchemaConnectionParams struct {
	SchemaID     int64 `json:"schema_id"`
	ConnectionID int64 `json:"connection_id"`
	IsDefault    int64 `json:"is_default"`
}

// Schema Connection Association Queries
// Associates a schema with a connection.
func (q *Queries) CreateSchemaConnection(ctx context.Context, arg CreateSchemaConnectionParams) (SchemaConnection, error) {
	row := q.db.QueryRowContext(ctx, createSchemaConnection, arg.SchemaID, arg.ConnectionID, arg.IsDefault)
	var i SchemaConnection
	err := row.Scan(
		&i.SchemaID,
		&i.ConnectionID,
		&i.IsDefault,
		&i.CreatedAt,
	)
	return i, err
}

const deleteConnection = `-- name: DeleteConnection :exec
DELETE FROM connections WHERE id = ?
`

// Deletes a connection by its ID.
func (q *Queries) DeleteConnection(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteConnection, id)
	return err
}

const deleteConnectionByName = `-- name: DeleteConnectionByName :exec
DELETE FROM connections WHERE name = ?
`

// Deletes a connection by its name.
func (q *Queries) DeleteConnectionByName(ctx context.Context, name string) error {
	_, err := q.db.ExecContext(ctx, deleteConnectionByName, name)
	return err
}

const deleteOpenAPIOperation = `-- name: DeleteOpenAPIOperation :exec
DELETE FROM openapi_operations WHERE id = ?
`

// Deletes an OpenAPI operation by its ID.
func (q *Queries) DeleteOpenAPIOperation(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteOpenAPIOperation, id)
	return err
}

const deleteOpenAPISchema = `-- name: DeleteOpenAPISchema :exec
DELETE FROM openapi_schemas WHERE id = ?
`

// Deletes an OpenAPI schema by its ID.
func (q *Queries) DeleteOpenAPISchema(ctx context.Context, id int64) error {
	_, err := q.db.ExecContext(ctx, deleteOpenAPISchema, id)
	return err
}

const deleteSchemaConnection = `-- name: DeleteSchemaConnection :exec
DELETE FROM schema_connections 
WHERE schema_id = ? AND connection_id = ?
`

type DeleteSchemaConnectionParams struct {
	SchemaID     int64 `json:"schema_id"`
	ConnectionID int64 `json:"connection_id"`
}

// Removes a schema-connection association.
func (q *Queries) DeleteSchemaConnection(ctx context.Context, arg DeleteSchemaConnectionParams) error {
	_, err := q.db.ExecContext(ctx, deleteSchemaConnection, arg.SchemaID, arg.ConnectionID)
	return err
}

const deleteSchemaConnectionsByConnection = `-- name: DeleteSchemaConnectionsByConnection :exec
DELETE FROM schema_connections WHERE connection_id = ?
`

// Removes all schemas for a connection.
func (q *Queries) DeleteSchemaConnectionsByConnection(ctx context.Context, connectionID int64) error {
	_, err := q.db.ExecContext(ctx, deleteSchemaConnectionsByConnection, connectionID)
	return err
}

const deleteSchemaConnectionsBySchema = `-- name: DeleteSchemaConnectionsBySchema :exec
DELETE FROM schema_connections WHERE schema_id = ?
`

// Removes all connections for a schema.
func (q *Queries) DeleteSchemaConnectionsBySchema(ctx context.Context, schemaID int64) error {
	_, err := q.db.ExecContext(ctx, deleteSchemaConnectionsBySchema, schemaID)
	return err
}

const getConnectionByID = `-- name: GetConnectionByID :one
SELECT id, name, description, base_url, auth_type, auth_username, auth_password_encrypted, auth_token_encrypted, auth_header_name, auth_header_value_encrypted, created_at, updated_at FROM connections WHERE id = ? LIMIT 1
`

// Retrieves a connection by its ID.
func (q *Queries) GetConnectionByID(ctx context.Context, id int64) (Connection, error) {
	row := q.db.QueryRowContext(ctx, getConnectionByID, id)
	var i Connection
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.BaseUrl,
		&i.AuthType,
		&i.AuthUsername,
		&i.AuthPasswordEncrypted,
		&i.AuthTokenEncrypted,
		&i.AuthHeaderName,
		&i.AuthHeaderValueEncrypted,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getConnectionByName = `-- name: GetConnectionByName :one
SELECT id, name, description, base_url, auth_type, auth_username, auth_password_encrypted, auth_token_encrypted, auth_header_name, auth_header_value_encrypted, created_at, updated_at FROM connections WHERE name = ? LIMIT 1
`

// Retrieves a connection by its name.
func (q *Queries) GetConnectionByName(ctx context.Context, name string) (Connection, error) {
	row := q.db.QueryRowContext(ctx, getConnectionByName, name)
	var i Connection
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.BaseUrl,
		&i.AuthType,
		&i.AuthUsername,
		&i.AuthPasswordEncrypted,
		&i.AuthTokenEncrypted,
		&i.AuthHeaderName,
		&i.AuthHeaderValueEncrypted,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getDefaultConnectionForSchema = `-- name: GetDefaultConnectionForSchema :one
SELECT c.id, c.name, c.description, c.base_url, c.auth_type, c.auth_username, c.auth_password_encrypted, c.auth_token_encrypted, c.auth_header_name, c.auth_header_value_encrypted, c.created_at, c.updated_at FROM connections c
JOIN schema_connections sc ON c.id = sc.connection_id
WHERE sc.schema_id = ? AND sc.is_default = 1
LIMIT 1
`

// Gets the default connection for a schema.
func (q *Queries) GetDefaultConnectionForSchema(ctx context.Context, schemaID int64) (Connection, error) {
	row := q.db.QueryRowContext(ctx, getDefaultConnectionForSchema, schemaID)
	var i Connection
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.BaseUrl,
		&i.AuthType,
		&i.AuthUsername,
		&i.AuthPasswordEncrypted,
		&i.AuthTokenEncrypted,
		&i.AuthHeaderName,
		&i.AuthHeaderValueEncrypted,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getOpenAPIOperationByID = `-- name: GetOpenAPIOperationByID :one
SELECT id, schema_id, operation_id, path, method, summary, description, created_at FROM openapi_operations WHERE id = ? LIMIT 1
`

// Retrieves an OpenAPI operation by its ID.
func (q *Queries) GetOpenAPIOperationByID(ctx context.Context, id int64) (OpenapiOperation, error) {
	row := q.db.QueryRowContext(ctx, getOpenAPIOperationByID, id)
	var i OpenapiOperation
	err := row.Scan(
		&i.ID,
		&i.SchemaID,
		&i.OperationID,
		&i.Path,
		&i.Method,
		&i.Summary,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const getOpenAPIOperationBySchemaAndOperationID = `-- name: GetOpenAPIOperationBySchemaAndOperationID :one
SELECT id, schema_id, operation_id, path, method, summary, description, created_at FROM openapi_operations 
WHERE schema_id = ? AND operation_id = ? 
LIMIT 1
`

type GetOpenAPIOperationBySchemaAndOperationIDParams struct {
	SchemaID    int64  `json:"schema_id"`
	OperationID string `json:"operation_id"`
}

// Retrieves an OpenAPI operation by schema ID and operation ID.
func (q *Queries) GetOpenAPIOperationBySchemaAndOperationID(ctx context.Context, arg GetOpenAPIOperationBySchemaAndOperationIDParams) (OpenapiOperation, error) {
	row := q.db.QueryRowContext(ctx, getOpenAPIOperationBySchemaAndOperationID, arg.SchemaID, arg.OperationID)
	var i OpenapiOperation
	err := row.Scan(
		&i.ID,
		&i.SchemaID,
		&i.OperationID,
		&i.Path,
		&i.Method,
		&i.Summary,
		&i.Description,
		&i.CreatedAt,
	)
	return i, err
}

const getOpenAPISchemaByFilename = `-- name: GetOpenAPISchemaByFilename :one
SELECT id, name, description, filename, version, created_at, updated_at FROM openapi_schemas WHERE filename = ? LIMIT 1
`

// Retrieves an OpenAPI schema by its filename.
func (q *Queries) GetOpenAPISchemaByFilename(ctx context.Context, filename string) (OpenapiSchema, error) {
	row := q.db.QueryRowContext(ctx, getOpenAPISchemaByFilename, filename)
	var i OpenapiSchema
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Filename,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getOpenAPISchemaByID = `-- name: GetOpenAPISchemaByID :one
SELECT id, name, description, filename, version, created_at, updated_at FROM openapi_schemas WHERE id = ? LIMIT 1
`

// Retrieves an OpenAPI schema by its ID.
func (q *Queries) GetOpenAPISchemaByID(ctx context.Context, id int64) (OpenapiSchema, error) {
	row := q.db.QueryRowContext(ctx, getOpenAPISchemaByID, id)
	var i OpenapiSchema
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Filename,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getOpenAPISchemaByName = `-- name: GetOpenAPISchemaByName :one
SELECT id, name, description, filename, version, created_at, updated_at FROM openapi_schemas WHERE name = ? LIMIT 1
`

// Retrieves an OpenAPI schema by its name.
func (q *Queries) GetOpenAPISchemaByName(ctx context.Context, name string) (OpenapiSchema, error) {
	row := q.db.QueryRowContext(ctx, getOpenAPISchemaByName, name)
	var i OpenapiSchema
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Filename,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getSchemaConnection = `-- name: GetSchemaConnection :one
SELECT schema_id, connection_id, is_default, created_at FROM schema_connections 
WHERE schema_id = ? AND connection_id = ? 
LIMIT 1
`

type GetSchemaConnectionParams struct {
	SchemaID     int64 `json:"schema_id"`
	ConnectionID int64 `json:"connection_id"`
}

// Gets a specific schema-connection association.
func (q *Queries) GetSchemaConnection(ctx context.Context, arg GetSchemaConnectionParams) (SchemaConnection, error) {
	row := q.db.QueryRowContext(ctx, getSchemaConnection, arg.SchemaID, arg.ConnectionID)
	var i SchemaConnection
	err := row.Scan(
		&i.SchemaID,
		&i.ConnectionID,
		&i.IsDefault,
		&i.CreatedAt,
	)
	return i, err
}

const getToolWithConnectionInfo = `-- name: GetToolWithConnectionInfo :one

SELECT 
    t.id as tool_id,
    t.tool_name,
    t.description as tool_description,
    t.input_schema,
    m.id as mapping_id,
    m.openapi_path,
    m.http_method,
    m.param_mappings,
    m.body_mapping,
    o.id as operation_id,
    o.operation_id as openapi_operation_id,
    s.id as schema_id,
    s.name as schema_name,
    s.filename as schema_filename,
    c.id as connection_id,
    c.name as connection_name,
    c.base_url,
    c.auth_type,
    c.auth_username,
    c.auth_password_encrypted,
    c.auth_token_encrypted,
    c.auth_header_name,
    c.auth_header_value_encrypted
FROM mcp_tools t
JOIN mcp_tool_mappings m ON t.id = m.mcp_tool_id
LEFT JOIN openapi_operations o ON m.operation_id = o.id
LEFT JOIN openapi_schemas s ON o.schema_id = s.id
LEFT JOIN schema_connections sc ON s.id = sc.schema_id AND sc.is_default = 1
LEFT JOIN connections c ON sc.connection_id = c.id
WHERE t.tool_name = ?
`

type GetToolWithConnectionInfoRow struct {
	ToolID                   int64          `json:"tool_id"`
	ToolName                 string         `json:"tool_name"`
	ToolDescription          sql.NullString `json:"tool_description"`
	InputSchema              string         `json:"input_schema"`
	MappingID                int64          `json:"mapping_id"`
	OpenapiPath              string         `json:"openapi_path"`
	HttpMethod               string         `json:"http_method"`
	ParamMappings            sql.NullString `json:"param_mappings"`
	BodyMapping              sql.NullString `json:"body_mapping"`
	OperationID              sql.NullInt64  `json:"operation_id"`
	OpenapiOperationID       sql.NullString `json:"openapi_operation_id"`
	SchemaID                 sql.NullInt64  `json:"schema_id"`
	SchemaName               sql.NullString `json:"schema_name"`
	SchemaFilename           sql.NullString `json:"schema_filename"`
	ConnectionID             sql.NullInt64  `json:"connection_id"`
	ConnectionName           sql.NullString `json:"connection_name"`
	BaseUrl                  sql.NullString `json:"base_url"`
	AuthType                 sql.NullString `json:"auth_type"`
	AuthUsername             sql.NullString `json:"auth_username"`
	AuthPasswordEncrypted    []byte         `json:"auth_password_encrypted"`
	AuthTokenEncrypted       []byte         `json:"auth_token_encrypted"`
	AuthHeaderName           sql.NullString `json:"auth_header_name"`
	AuthHeaderValueEncrypted []byte         `json:"auth_header_value_encrypted"`
}

// Enhanced Tool Queries
// Retrieves an MCP tool with its mapping and connection information.
func (q *Queries) GetToolWithConnectionInfo(ctx context.Context, toolName string) (GetToolWithConnectionInfoRow, error) {
	row := q.db.QueryRowContext(ctx, getToolWithConnectionInfo, toolName)
	var i GetToolWithConnectionInfoRow
	err := row.Scan(
		&i.ToolID,
		&i.ToolName,
		&i.ToolDescription,
		&i.InputSchema,
		&i.MappingID,
		&i.OpenapiPath,
		&i.HttpMethod,
		&i.ParamMappings,
		&i.BodyMapping,
		&i.OperationID,
		&i.OpenapiOperationID,
		&i.SchemaID,
		&i.SchemaName,
		&i.SchemaFilename,
		&i.ConnectionID,
		&i.ConnectionName,
		&i.BaseUrl,
		&i.AuthType,
		&i.AuthUsername,
		&i.AuthPasswordEncrypted,
		&i.AuthTokenEncrypted,
		&i.AuthHeaderName,
		&i.AuthHeaderValueEncrypted,
	)
	return i, err
}

const listConnections = `-- name: ListConnections :many
SELECT id, name, description, base_url, auth_type, auth_username, auth_password_encrypted, auth_token_encrypted, auth_header_name, auth_header_value_encrypted, created_at, updated_at FROM connections ORDER BY name
`

// Lists all connections.
func (q *Queries) ListConnections(ctx context.Context) ([]Connection, error) {
	rows, err := q.db.QueryContext(ctx, listConnections)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Connection
	for rows.Next() {
		var i Connection
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.BaseUrl,
			&i.AuthType,
			&i.AuthUsername,
			&i.AuthPasswordEncrypted,
			&i.AuthTokenEncrypted,
			&i.AuthHeaderName,
			&i.AuthHeaderValueEncrypted,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listConnectionsBySchema = `-- name: ListConnectionsBySchema :many
SELECT c.id, c.name, c.description, c.base_url, c.auth_type, c.auth_username, c.auth_password_encrypted, c.auth_token_encrypted, c.auth_header_name, c.auth_header_value_encrypted, c.created_at, c.updated_at FROM connections c
JOIN schema_connections sc ON c.id = sc.connection_id
WHERE sc.schema_id = ?
ORDER BY sc.is_default DESC, c.name
`

// Lists all connections associated with a schema.
func (q *Queries) ListConnectionsBySchema(ctx context.Context, schemaID int64) ([]Connection, error) {
	rows, err := q.db.QueryContext(ctx, listConnectionsBySchema, schemaID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Connection
	for rows.Next() {
		var i Connection
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.BaseUrl,
			&i.AuthType,
			&i.AuthUsername,
			&i.AuthPasswordEncrypted,
			&i.AuthTokenEncrypted,
			&i.AuthHeaderName,
			&i.AuthHeaderValueEncrypted,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOpenAPIOperationsBySchema = `-- name: ListOpenAPIOperationsBySchema :many
SELECT id, schema_id, operation_id, path, method, summary, description, created_at FROM openapi_operations 
WHERE schema_id = ? 
ORDER BY path, method
`

// Lists all operations for a specific schema.
func (q *Queries) ListOpenAPIOperationsBySchema(ctx context.Context, schemaID int64) ([]OpenapiOperation, error) {
	rows, err := q.db.QueryContext(ctx, listOpenAPIOperationsBySchema, schemaID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []OpenapiOperation
	for rows.Next() {
		var i OpenapiOperation
		if err := rows.Scan(
			&i.ID,
			&i.SchemaID,
			&i.OperationID,
			&i.Path,
			&i.Method,
			&i.Summary,
			&i.Description,
			&i.CreatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listOpenAPISchemas = `-- name: ListOpenAPISchemas :many
SELECT id, name, description, filename, version, created_at, updated_at FROM openapi_schemas ORDER BY name
`

// Lists all OpenAPI schemas.
func (q *Queries) ListOpenAPISchemas(ctx context.Context) ([]OpenapiSchema, error) {
	rows, err := q.db.QueryContext(ctx, listOpenAPISchemas)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []OpenapiSchema
	for rows.Next() {
		var i OpenapiSchema
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Filename,
			&i.Version,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listSchemasByConnection = `-- name: ListSchemasByConnection :many
SELECT s.id, s.name, s.description, s.filename, s.version, s.created_at, s.updated_at FROM openapi_schemas s
JOIN schema_connections sc ON s.id = sc.schema_id
WHERE sc.connection_id = ?
ORDER BY s.name
`

// Lists all schemas associated with a connection.
func (q *Queries) ListSchemasByConnection(ctx context.Context, connectionID int64) ([]OpenapiSchema, error) {
	rows, err := q.db.QueryContext(ctx, listSchemasByConnection, connectionID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []OpenapiSchema
	for rows.Next() {
		var i OpenapiSchema
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.Filename,
			&i.Version,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const setDefaultConnection = `-- name: SetDefaultConnection :exec
UPDATE schema_connections 
SET is_default = CASE WHEN connection_id = ? THEN 1 ELSE 0 END
WHERE schema_id = ?
`

type SetDefaultConnectionParams struct {
	ConnectionID int64 `json:"connection_id"`
	SchemaID     int64 `json:"schema_id"`
}

// Sets a connection as the default for a schema (removes default from others).
func (q *Queries) SetDefaultConnection(ctx context.Context, arg SetDefaultConnectionParams) error {
	_, err := q.db.ExecContext(ctx, setDefaultConnection, arg.ConnectionID, arg.SchemaID)
	return err
}

const updateConnection = `-- name: UpdateConnection :one
UPDATE connections 
SET 
    name = COALESCE(?, name),
    description = COALESCE(?, description),
    base_url = COALESCE(?, base_url),
    auth_type = COALESCE(?, auth_type),
    auth_username = COALESCE(?, auth_username),
    auth_password_encrypted = COALESCE(?, auth_password_encrypted),
    auth_token_encrypted = COALESCE(?, auth_token_encrypted),
    auth_header_name = COALESCE(?, auth_header_name),
    auth_header_value_encrypted = COALESCE(?, auth_header_value_encrypted),
    updated_at = CURRENT_TIMESTAMP
WHERE id = ? 
RETURNING id, name, description, base_url, auth_type, auth_username, auth_password_encrypted, auth_token_encrypted, auth_header_name, auth_header_value_encrypted, created_at, updated_at
`

type UpdateConnectionParams struct {
	Name                     string         `json:"name"`
	Description              sql.NullString `json:"description"`
	BaseUrl                  string         `json:"base_url"`
	AuthType                 string         `json:"auth_type"`
	AuthUsername             sql.NullString `json:"auth_username"`
	AuthPasswordEncrypted    []byte         `json:"auth_password_encrypted"`
	AuthTokenEncrypted       []byte         `json:"auth_token_encrypted"`
	AuthHeaderName           sql.NullString `json:"auth_header_name"`
	AuthHeaderValueEncrypted []byte         `json:"auth_header_value_encrypted"`
	ID                       int64          `json:"id"`
}

// Updates an existing connection.
func (q *Queries) UpdateConnection(ctx context.Context, arg UpdateConnectionParams) (Connection, error) {
	row := q.db.QueryRowContext(ctx, updateConnection,
		arg.Name,
		arg.Description,
		arg.BaseUrl,
		arg.AuthType,
		arg.AuthUsername,
		arg.AuthPasswordEncrypted,
		arg.AuthTokenEncrypted,
		arg.AuthHeaderName,
		arg.AuthHeaderValueEncrypted,
		arg.ID,
	)
	var i Connection
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.BaseUrl,
		&i.AuthType,
		&i.AuthUsername,
		&i.AuthPasswordEncrypted,
		&i.AuthTokenEncrypted,
		&i.AuthHeaderName,
		&i.AuthHeaderValueEncrypted,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateOpenAPISchema = `-- name: UpdateOpenAPISchema :one
UPDATE openapi_schemas 
SET 
    name = COALESCE(?, name),
    description = COALESCE(?, description),
    filename = COALESCE(?, filename),
    version = COALESCE(?, version),
    updated_at = CURRENT_TIMESTAMP
WHERE id = ? 
RETURNING id, name, description, filename, version, created_at, updated_at
`

type UpdateOpenAPISchemaParams struct {
	Name        string         `json:"name"`
	Description sql.NullString `json:"description"`
	Filename    string         `json:"filename"`
	Version     sql.NullString `json:"version"`
	ID          int64          `json:"id"`
}

// Updates an existing OpenAPI schema.
func (q *Queries) UpdateOpenAPISchema(ctx context.Context, arg UpdateOpenAPISchemaParams) (OpenapiSchema, error) {
	row := q.db.QueryRowContext(ctx, updateOpenAPISchema,
		arg.Name,
		arg.Description,
		arg.Filename,
		arg.Version,
		arg.ID,
	)
	var i OpenapiSchema
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Description,
		&i.Filename,
		&i.Version,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
