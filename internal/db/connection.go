package db

import (
	"context"
	"database/sql"
	"fmt"
	"net/url"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
	_ "github.com/mattn/go-sqlite3"
)

// DatabaseConnection represents a generic database connection interface
type DatabaseConnection interface {
	Close(ctx context.Context) error
	Ping(ctx context.Context) error
	GetQueries() *Queries
}

// PostgreSQLConnection wraps a pgx connection
type PostgreSQLConnection struct {
	conn    *pgx.Conn
	queries *Queries
}

func (p *PostgreSQLConnection) Close(ctx context.Context) error {
	return p.conn.Close(ctx)
}

func (p *PostgreSQLConnection) Ping(ctx context.Context) error {
	return p.conn.Ping(ctx)
}

func (p *PostgreSQLConnection) GetQueries() *Queries {
	return p.queries
}

// SQLiteConnection wraps a sql.DB connection for SQLite
type SQLiteConnection struct {
	db      *sql.DB
	queries *Queries
}

func (s *SQLiteConnection) Close(ctx context.Context) error {
	return s.db.Close()
}

func (s *SQLiteConnection) Ping(ctx context.Context) error {
	return s.db.PingContext(ctx)
}

func (s *SQLiteConnection) GetQueries() *Queries {
	return s.queries
}

// ConnectDB establishes a connection to either PostgreSQL or SQLite based on the connection string scheme.
// For backward compatibility, this returns a DBTX interface that can be used with db.New()
var ConnectDB = func(connectionString string) (DBTX, error) {
	parsedURL, err := url.Parse(connectionString)
	if err != nil {
		return nil, fmt.Errorf("invalid connection string format: %w", err)
	}

	switch strings.ToLower(parsedURL.Scheme) {
	case "postgres", "postgresql":
		return connectPostgreSQLDirect(connectionString)
	case "sqlite", "sqlite3":
		return connectSQLiteDirect(connectionString)
	default:
		return nil, fmt.Errorf("unsupported database scheme: %s", parsedURL.Scheme)
	}
}

// ConnectDatabase creates a database connection based on the connection string scheme
func ConnectDatabase(connectionString string) (DatabaseConnection, error) {
	parsedURL, err := url.Parse(connectionString)
	if err != nil {
		return nil, fmt.Errorf("invalid connection string format: %w", err)
	}

	switch strings.ToLower(parsedURL.Scheme) {
	case "postgres", "postgresql":
		return connectPostgreSQL(connectionString)
	case "sqlite", "sqlite3":
		return connectSQLite(connectionString)
	default:
		return nil, fmt.Errorf("unsupported database scheme: %s", parsedURL.Scheme)
	}
}

// connectPostgreSQL establishes a PostgreSQL connection
func connectPostgreSQL(connectionString string) (*PostgreSQLConnection, error) {
	conn, err := pgx.Connect(context.Background(), connectionString)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to PostgreSQL database: %w", err)
	}

	err = conn.Ping(context.Background())
	if err != nil {
		conn.Close(context.Background())
		return nil, fmt.Errorf("PostgreSQL database ping failed: %w", err)
	}

	queries := New(conn)
	return &PostgreSQLConnection{
		conn:    conn,
		queries: queries,
	}, nil
}

// connectSQLite establishes a SQLite connection
func connectSQLite(connectionString string) (*SQLiteConnection, error) {
	// Parse SQLite connection string (sqlite://path/to/database.db)
	parsedURL, err := url.Parse(connectionString)
	if err != nil {
		return nil, fmt.Errorf("invalid SQLite connection string: %w", err)
	}

	// Extract the database path - handle both sqlite://./path and sqlite:///absolute/path
	dbPath := parsedURL.Path
	if dbPath == "" && parsedURL.Opaque != "" {
		// Handle sqlite:path format
		dbPath = parsedURL.Opaque
	}
	if dbPath == "" {
		return nil, fmt.Errorf("SQLite database path is required")
	}

	// Remove leading slash for relative paths
	if strings.HasPrefix(dbPath, "/./") {
		dbPath = dbPath[1:] // Remove leading slash, keep ./
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to SQLite database: %w", err)
	}

	err = db.PingContext(context.Background())
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("SQLite database ping failed: %w", err)
	}

	// Create a wrapper that implements the DBTX interface for SQLite
	sqliteWrapper := &SQLiteDBTX{db: db}
	queries := New(sqliteWrapper)

	return &SQLiteConnection{
		db:      db,
		queries: queries,
	}, nil
}

// SQLiteDBTX wraps sql.DB to implement the DBTX interface for SQLite
type SQLiteDBTX struct {
	db *sql.DB
}

func (s *SQLiteDBTX) Exec(ctx context.Context, query string, args ...interface{}) (pgconn.CommandTag, error) {
	result, err := s.db.ExecContext(ctx, query, args...)
	if err != nil {
		return pgconn.CommandTag{}, err
	}

	rowsAffected, err := result.RowsAffected()
	if err != nil {
		return pgconn.CommandTag{}, err
	}

	// Create a mock CommandTag for SQLite
	return pgconn.NewCommandTag(fmt.Sprintf("UPDATE %d", rowsAffected)), nil
}

func (s *SQLiteDBTX) Query(ctx context.Context, query string, args ...interface{}) (pgx.Rows, error) {
	rows, err := s.db.QueryContext(ctx, query, args...)
	if err != nil {
		return nil, err
	}

	// Wrap sql.Rows to implement pgx.Rows interface
	return &SQLiteRowsWrapper{rows: rows}, nil
}

func (s *SQLiteDBTX) QueryRow(ctx context.Context, query string, args ...interface{}) pgx.Row {
	row := s.db.QueryRowContext(ctx, query, args...)
	return &SQLiteRowWrapper{row: row}
}

// SQLiteRowsWrapper wraps sql.Rows to implement pgx.Rows interface
type SQLiteRowsWrapper struct {
	rows *sql.Rows
}

func (s *SQLiteRowsWrapper) Close() {
	s.rows.Close()
}

func (s *SQLiteRowsWrapper) Err() error {
	return s.rows.Err()
}

func (s *SQLiteRowsWrapper) Next() bool {
	return s.rows.Next()
}

func (s *SQLiteRowsWrapper) Scan(dest ...interface{}) error {
	return s.rows.Scan(dest...)
}

func (s *SQLiteRowsWrapper) Values() ([]interface{}, error) {
	// This is a simplified implementation
	// In a real implementation, you might need to handle this differently
	return nil, fmt.Errorf("Values() not implemented for SQLite wrapper")
}

func (s *SQLiteRowsWrapper) RawValues() [][]byte {
	// This is a simplified implementation
	return nil
}

func (s *SQLiteRowsWrapper) FieldDescriptions() []pgconn.FieldDescription {
	// This is a simplified implementation
	return nil
}

func (s *SQLiteRowsWrapper) CommandTag() pgconn.CommandTag {
	return pgconn.CommandTag{}
}

func (s *SQLiteRowsWrapper) Conn() *pgx.Conn {
	// SQLite doesn't have a pgx.Conn, so return nil
	// This method is required by the pgx.Rows interface but not used in our case
	return nil
}

// SQLiteRowWrapper wraps sql.Row to implement pgx.Row interface
type SQLiteRowWrapper struct {
	row *sql.Row
}

func (s *SQLiteRowWrapper) Scan(dest ...interface{}) error {
	return s.row.Scan(dest...)
}

// connectPostgreSQLDirect establishes a PostgreSQL connection and returns DBTX interface
func connectPostgreSQLDirect(connectionString string) (DBTX, error) {
	conn, err := pgx.Connect(context.Background(), connectionString)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to PostgreSQL database: %w", err)
	}

	err = conn.Ping(context.Background())
	if err != nil {
		conn.Close(context.Background())
		return nil, fmt.Errorf("PostgreSQL database ping failed: %w", err)
	}

	return conn, nil
}

// connectSQLiteDirect establishes a SQLite connection and returns DBTX interface
func connectSQLiteDirect(connectionString string) (DBTX, error) {
	// Parse SQLite connection string (sqlite://path/to/database.db)
	parsedURL, err := url.Parse(connectionString)
	if err != nil {
		return nil, fmt.Errorf("invalid SQLite connection string: %w", err)
	}

	// Extract the database path - handle both sqlite://./path and sqlite:///absolute/path
	dbPath := parsedURL.Path
	if dbPath == "" && parsedURL.Opaque != "" {
		// Handle sqlite:path format
		dbPath = parsedURL.Opaque
	}
	if dbPath == "" {
		return nil, fmt.Errorf("SQLite database path is required")
	}

	// Remove leading slash for relative paths
	if strings.HasPrefix(dbPath, "/./") {
		dbPath = dbPath[1:] // Remove leading slash, keep ./
	}

	db, err := sql.Open("sqlite3", dbPath)
	if err != nil {
		return nil, fmt.Errorf("unable to connect to SQLite database: %w", err)
	}

	err = db.PingContext(context.Background())
	if err != nil {
		db.Close()
		return nil, fmt.Errorf("SQLite database ping failed: %w", err)
	}

	// Return the SQLite wrapper that implements DBTX
	return &SQLiteDBTX{db: db}, nil
}
