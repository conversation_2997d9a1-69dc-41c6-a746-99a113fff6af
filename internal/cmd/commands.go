package cmd

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"

	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/schema"
	"github.com/ravan/suse-air/internal/server"
	"github.com/ravan/suse-air/internal/tui"
	"github.com/ravan/suse-air/internal/util"

	"github.com/charmbracelet/bubbletea"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/urfave/cli/v2"
	"golang.org/x/crypto/bcrypt"
)

// connectDatabase is a helper function to establish database connection and return queries
func connectDatabase(dbConnectionString string) (*db.Queries, func(), error) {
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return nil, nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	cleanup := func() {
		conn.Close(context.Background())
	}

	return conn.GetQueries(), cleanup, nil
}

func RunConversion(dbConnectionString, filePath string) error {

	log.Println("--- Running in CONVERT mode ---")
	// Read and Parse OpenAPI File
	fileBytes, err := os.ReadFile(filePath)
	if err != nil {
		return fmt.Errorf("failed to read file %s: %w", filePath, err)
	}
	var openAPISpec models.OpenAPISpec
	if err := json.Unmarshal(fileBytes, &openAPISpec); err != nil {
		return fmt.Errorf("failed to unmarshal OpenAPI JSON: %w", err)
	}

	// Convert to MCP Tools and Mappings
	conversionResults, err := schema.ProcessOpenAPISpec(&openAPISpec)
	if err != nil {
		return fmt.Errorf("failed to convert OpenAPI spec: %w", err)
	}

	// Store Results in Database
	ctx := context.Background()
	queries, cleanup, err := connectDatabase(dbConnectionString)
	if err != nil {
		return err
	}
	defer cleanup()

	// Create OpenAPI schema record
	schemaName := generateSchemaName(filePath)
	schemaParams := db.CreateOpenAPISchemaParams{
		Name:     schemaName,
		Filename: filePath,
	}

	// Try to extract version from the spec
	if info, ok := openAPISpec.Components["info"].(map[string]interface{}); ok {
		if version, ok := info["version"].(string); ok {
			schemaParams.Version = pgtype.Text{String: version, Valid: true}
		}
		if title, ok := info["title"].(string); ok {
			schemaParams.Description = pgtype.Text{String: title, Valid: true}
		}
	}

	savedSchema, err := queries.CreateOpenAPISchema(ctx, schemaParams)
	if err != nil {
		return fmt.Errorf("failed to create schema record: %w", err)
	}
	log.Printf("DEBUG: Created schema record with ID: %d, Name: %s", savedSchema.ID, savedSchema.Name)

	var createdCount int
	for _, result := range conversionResults {
		// Create OpenAPI operation record
		operationParams := db.CreateOpenAPIOperationParams{
			SchemaID:    savedSchema.ID,
			OperationID: result.Tool.Name, // Use tool name as operation ID
			Path:        result.Mapping.OpenAPIPath,
			Method:      result.Mapping.HTTPMethod,
		}

		if result.Tool.Description != "" {
			operationParams.Summary = pgtype.Text{String: result.Tool.Description, Valid: true}
		}

		savedOperation, err := queries.CreateOpenAPIOperation(ctx, operationParams)
		if err != nil {
			log.Printf("ERROR: Could not save operation %s: %v", result.Tool.Name, err)
			continue
		}
		log.Printf("DEBUG: Saved operation with ID: %d, OperationID: %s", savedOperation.ID, savedOperation.OperationID)

		schemaBytes, _ := json.Marshal(result.Tool.InputSchema)
		paramMappingsBytes, _ := json.Marshal(result.Mapping.ParamMappings)
		bodyMappingBytes, _ := json.Marshal(result.Mapping.BodyMapping)

		toolParams := db.CreateMCPToolParams{
			ToolName:    result.Tool.Name,
			Description: pgtype.Text{String: result.Tool.Description, Valid: result.Tool.Description != ""},
			InputSchema: schemaBytes,
		}
		savedTool, err := queries.CreateMCPTool(ctx, toolParams)
		if err != nil {
			log.Printf("ERROR: Could not save tool %s: %v", result.Tool.Name, err)
			continue
		}
		log.Printf("DEBUG: Saved tool with ID: %d, Name: %s", savedTool.ID, savedTool.ToolName)

		mappingParams := db.CreateMCPToolMappingParams{
			McpToolID:     savedTool.ID,
			OpenapiPath:   result.Mapping.OpenAPIPath,
			HttpMethod:    result.Mapping.HTTPMethod,
			ParamMappings: paramMappingsBytes,
			BodyMapping:   bodyMappingBytes,
			OperationID:   pgtype.Int8{Int64: savedOperation.ID, Valid: true}, // Link to operation
		}
		_, err = queries.CreateMCPToolMapping(ctx, mappingParams)
		if err != nil {
			log.Printf("ERROR: Could not save mapping for tool %s (tool ID %d): %v", result.Tool.Name, savedTool.ID, err)
			continue
		}
		log.Printf("DEBUG: Saved mapping for tool: %s (tool ID %d)", result.Tool.Name, savedTool.ID)
		createdCount++
	}
	log.Printf("Finished. Saved %d new tools with mappings.\n", createdCount)
	return nil
}

func RunExecution(dbConnectionString, toolName, args, baseURL string) error {
	log.Println("--- Running in EXECUTE mode ---")

	// Validate baseURL
	if baseURL == "" || !strings.HasPrefix(baseURL, "http") {
		return fmt.Errorf("the -base-url flag is required and must be a valid URL")
	}

	ctx := context.Background()
	queries, cleanup, err := connectDatabase(dbConnectionString)
	if err != nil {
		return err
	}
	defer cleanup()

	// 1. Fetch the tool and its mapping from the DB
	log.Printf("Fetching mapping for tool: %s\n", toolName)
	toolWithMapping, err := queries.GetToolWithMapping(ctx, toolName)
	if err != nil {
		return fmt.Errorf("error fetching tool '%s' from database: %w", toolName, err)
	}

	// 2. Unmarshal the provided arguments
	var mcpArgs map[string]interface{}
	if err := json.Unmarshal([]byte(args), &mcpArgs); err != nil {
		return fmt.Errorf("invalid JSON provided for -args flag: %w", err)
	}

	// 3. Use the executor to build the HTTP request
	log.Println("Building HTTP request from MCP arguments...")
	req, err := schema.BuildRequest(toolWithMapping, mcpArgs, baseURL)
	if err != nil {
		return fmt.Errorf("failed to build HTTP request: %w", err)
	}

	// 4. Execute the request
	log.Printf("Executing request: %s %s\n", req.Method, req.URL.String())
	client := schema.NewAPIClient()
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute API request: %w", err)
	}
	defer resp.Body.Close()

	// 5. Print the result
	log.Printf("API Response Status: %s\n", resp.Status)
	fmt.Println("--- API Response Body ---")
	if _, err := os.Stdout.ReadFrom(resp.Body); err != nil {
		log.Printf("Warning: could not read response body: %v", err)
	}
	fmt.Println("\n--- End Response ---")
	return nil
}

// RunExecutionWithConnection executes a tool using connection information for authentication
func RunExecutionWithConnection(dbConnectionString, toolName, args string) error {
	log.Println("--- Running in EXECUTE mode with connection ---")

	ctx := context.Background()
	queries, cleanup, err := connectDatabase(dbConnectionString)
	if err != nil {
		return err
	}
	defer cleanup()

	// 1. Fetch the tool with connection information
	log.Printf("Fetching tool with connection info for: %s\n", toolName)
	toolWithConnectionInfo, err := queries.GetToolWithConnectionInfo(ctx, toolName)
	if err != nil {
		return fmt.Errorf("error fetching tool '%s' with connection info: %w", toolName, err)
	}

	// Check if connection is available
	if !toolWithConnectionInfo.ConnectionID.Valid {
		return fmt.Errorf("no connection associated with tool '%s'. Please associate a connection with the schema first", toolName)
	}

	// 2. Unmarshal the provided arguments
	var mcpArgs map[string]interface{}
	if err := json.Unmarshal([]byte(args), &mcpArgs); err != nil {
		return fmt.Errorf("invalid JSON provided for args: %w", err)
	}

	// 3. Use the executor to build the HTTP request with authentication
	log.Println("Building HTTP request with authentication...")
	req, err := schema.BuildRequestWithConnection(toolWithConnectionInfo, mcpArgs)
	if err != nil {
		return fmt.Errorf("failed to build HTTP request: %w", err)
	}

	// 4. Execute the request
	log.Printf("Executing request: %s %s\n", req.Method, req.URL.String())
	log.Printf("Using connection: %s (%s)\n", toolWithConnectionInfo.ConnectionName.String, toolWithConnectionInfo.BaseUrl.String)
	client := schema.NewAPIClient()
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to execute API request: %w", err)
	}
	defer resp.Body.Close()

	// 5. Print the result
	log.Printf("API Response Status: %s\n", resp.Status)
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}
	fmt.Printf("Response Body:\n%s\n", string(body))
	return nil
}

func RunServer(port int, dbConnectionString string) {
	queries, cleanup, err := connectDatabase(dbConnectionString)
	if err != nil {
		log.Fatalf("failed to connect to database: %v", err)
	}
	defer cleanup()
	log.Printf("--- Starting MCP Server on port %d ---", port)
	mcpServer := server.NewServer(queries)
	//authHandler := server.AuthMiddleware(mcpServer)
	profileRouter := server.ProfileRouter(mcpServer)
	http.Handle("/mcp/", profileRouter)

	log.Fatal(http.ListenAndServe(fmt.Sprintf(":%d", port), nil))
}

func RunProfileCreate(c *cli.Context) error {
	name := c.String("name")
	description := c.String("description")
	pathSegment := c.String("path-segment")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	params := db.CreateProfileParams{
		Name:        name,
		Description: pgtype.Text{String: description, Valid: description != ""},
		PathSegment: pathSegment,
	}

	profile, err := queries.CreateProfile(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to create profile: %w", err)
	}

	fmt.Printf("Profile created successfully: ID=%d, Name=%s, PathSegment=%s\n", profile.ID, profile.Name, profile.PathSegment)
	return nil
}

func RunProfileList(c *cli.Context) error {
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	profiles, err := queries.ListProfiles(ctx)
	if err != nil {
		return fmt.Errorf("failed to list profiles: %w", err)
	}

	if len(profiles) == 0 {
		fmt.Println("No profiles found.")
		return nil
	}

	fmt.Println("Profiles:")
	for _, p := range profiles {
		fmt.Printf("  ID: %d, Name: %s, Description: %s, PathSegment: %s\n", p.ID, p.Name, p.Description.String, p.PathSegment)
	}
	return nil
}

func RunProfileGet(c *cli.Context) error {
	name := c.String("name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	profile, err := queries.GetProfileByName(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to get profile %s: %w", name, err)
	}

	fmt.Printf("Profile Details:\n")
	fmt.Printf("  ID: %d\n", profile.ID)
	fmt.Printf("  Name: %s\n", profile.Name)
	fmt.Printf("  Description: %s\n", profile.Description.String)
	fmt.Printf("  PathSegment: %s\n", profile.PathSegment)
	fmt.Printf("  CreatedAt: %s\n", profile.CreatedAt.Time.Format("2006-01-02 15:04:05"))
	fmt.Printf("  UpdatedAt: %s\n", profile.UpdatedAt.Time.Format("2006-01-02 15:04:05"))

	return nil
}

func RunProfileUpdate(c *cli.Context) error {
	name := c.String("name")
	newName := c.String("new-name")
	newDescription := c.String("new-description")
	newPathSegment := c.String("new-path-segment")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// First, get the existing profile to update its fields
	existingProfile, err := queries.GetProfileByName(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to find profile %s for update: %w", name, err)
	}

	// Prepare update parameters, only updating if new values are provided
	updateParams := db.UpdateProfileParams{
		ID:          existingProfile.ID,
		Name:        existingProfile.Name,
		Description: existingProfile.Description,
		PathSegment: existingProfile.PathSegment,
	}

	if newName != "" {
		updateParams.Name = newName
	}
	if newDescription != "" {
		updateParams.Description = pgtype.Text{String: newDescription, Valid: true}
	}
	if newPathSegment != "" {
		updateParams.PathSegment = newPathSegment
	}

	profile, err := queries.UpdateProfile(ctx, updateParams)
	if err != nil {
		return fmt.Errorf("failed to update profile %s: %w", name, err)
	}

	fmt.Printf("Profile updated successfully: ID=%d, Name=%s, PathSegment=%s\n", profile.ID, profile.Name, profile.PathSegment)
	return nil
}

func RunProfileDelete(c *cli.Context) error {
	name := c.String("name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// First, get the existing profile to get its ID
	existingProfile, err := queries.GetProfileByName(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to find profile %s for deletion: %w", name, err)
	}

	err = queries.DeleteProfile(ctx, existingProfile.ID)
	if err != nil {
		return fmt.Errorf("failed to delete profile %s: %w", name, err)
	}

	fmt.Printf("Profile %s deleted successfully.\n", name)
	return nil
}

func RunToolAssociate(c *cli.Context) error {
	profileName := c.String("profile")
	toolName := c.String("tool")
	acl := c.String("acl")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	profile, err := queries.GetProfileByName(ctx, profileName)
	if err != nil {
		return fmt.Errorf("failed to get profile %s: %w", profileName, err)
	}

	tool, err := queries.GetMCPToolByName(ctx, toolName)
	if err != nil {
		return fmt.Errorf("failed to get tool %s: %w", toolName, err)
	}

	params := db.CreateProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool.ID,
		Acl:       acl,
	}

	_, err = queries.CreateProfileTool(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to associate tool %s with profile %s: %w", toolName, profileName, err)
	}

	fmt.Printf("Tool %s associated with profile %s with ACL %s successfully.\n", toolName, profileName, acl)
	return nil
}

func RunToolDisassociate(c *cli.Context) error {
	profileName := c.String("profile")
	toolName := c.String("tool")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	profile, err := queries.GetProfileByName(ctx, profileName)
	if err != nil {
		return fmt.Errorf("failed to get profile %s: %w", profileName, err)
	}

	tool, err := queries.GetMCPToolByName(ctx, toolName)
	if err != nil {
		return fmt.Errorf("failed to get tool %s: %w", toolName, err)
	}

	params := db.DeleteProfileToolParams{
		ProfileID: profile.ID,
		ToolID:    tool.ID,
	}

	err = queries.DeleteProfileTool(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to disassociate tool %s from profile %s: %w", toolName, profileName, err)
	}

	fmt.Printf("Tool %s disassociated from profile %s successfully.\n", toolName, profileName)
	return nil
}

func RunToolList(c *cli.Context) error {
	profileName := c.String("profile")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	tools, err := queries.ListMCPToolsByProfile(ctx, profileName)
	if err != nil {
		return fmt.Errorf("failed to list tools for profile %s: %w", profileName, err)
	}

	if len(tools) == 0 {
		fmt.Printf("No tools found for profile %s.\n", profileName)
		return nil
	}

	fmt.Printf("Tools for profile %s:\n", profileName)
	for _, t := range tools {
		fmt.Printf("  ID: %d, Name: %s, Description: %s\n", t.ID, t.ToolName, t.Description.String)
	}
	return nil
}

func RunUserCreate(c *cli.Context) error {
	username := c.String("username")
	password := c.String("password")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	queries, cleanup, err := connectDatabase(dbConnectionString)
	if err != nil {
		return err
	}
	defer cleanup()

	// Hash the password (using a simple placeholder for now)
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	params := db.CreateUserParams{
		Username:    username,
		PasswordHash: string(passwordHash),
	}

	user, err := queries.CreateUser(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	fmt.Printf("User created successfully: ID=%d, Username=%s\n", user.ID, user.Username)
	return nil
}

func RunUserList(c *cli.Context) error {
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	users, err := queries.ListUsers(ctx)
	if err != nil {
		return fmt.Errorf("failed to list users: %w", err)
	}

	if len(users) == 0 {
		fmt.Println("No users found.")
		return nil
	}

	fmt.Println("Users:")
	for _, u := range users {
		fmt.Printf("  ID: %d, Username: %s\n", u.ID, u.Username)
	}
	return nil
}

func RunUserAssignRole(c *cli.Context) error {
	username := c.String("username")
	roleName := c.String("role")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	user, err := queries.GetUserByUsername(ctx, username)
	if err != nil {
		return fmt.Errorf("failed to get user %s: %w", username, err)
	}

	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	params := db.CreateUserRoleParams{
		UserID: user.ID,
		RoleID: role.ID,
	}

	_, err = queries.CreateUserRole(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to assign role %s to user %s: %w", roleName, username, err)
	}

	fmt.Printf("Role %s assigned to user %s successfully.\n", roleName, username)
	return nil
}

func RunUserRemoveRole(c *cli.Context) error {
	username := c.String("username")
	roleName := c.String("role")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	user, err := queries.GetUserByUsername(ctx, username)
	if err != nil {
		return fmt.Errorf("failed to get user %s: %w", username, err)
	}

	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	params := db.DeleteUserRoleParams{
		UserID: user.ID,
		RoleID: role.ID,
	}

	err = queries.DeleteUserRole(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to remove role %s from user %s: %w", roleName, username, err)
	}

	fmt.Printf("Role %s removed from user %s successfully.\n", roleName, username)
	return nil
}

// Role Management Commands

func RunRoleCreate(c *cli.Context) error {
	roleName := c.String("name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	role, err := queries.CreateRole(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to create role %s: %w", roleName, err)
	}

	fmt.Printf("Role created successfully:\n")
	fmt.Printf("  ID: %d, Name: %s\n", role.ID, role.Name)
	return nil
}

func RunRoleList(c *cli.Context) error {
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	roles, err := queries.ListRoles(ctx)
	if err != nil {
		return fmt.Errorf("failed to list roles: %w", err)
	}

	if len(roles) == 0 {
		fmt.Println("No roles found.")
		return nil
	}

	fmt.Println("Roles:")
	for _, r := range roles {
		fmt.Printf("  ID: %d, Name: %s\n", r.ID, r.Name)
	}
	return nil
}

func RunRoleGet(c *cli.Context) error {
	roleName := c.String("name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	fmt.Printf("Role Details:\n")
	fmt.Printf("  ID: %d\n", role.ID)
	fmt.Printf("  Name: %s\n", role.Name)
	fmt.Printf("  Created: %s\n", role.CreatedAt.Time.Format("2006-01-02 15:04:05"))
	fmt.Printf("  Updated: %s\n", role.UpdatedAt.Time.Format("2006-01-02 15:04:05"))
	return nil
}

func RunRoleUpdate(c *cli.Context) error {
	roleName := c.String("name")
	newName := c.String("new-name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get the existing role
	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	// Update the role
	params := db.UpdateRoleParams{
		ID:   role.ID,
		Name: newName,
	}

	updatedRole, err := queries.UpdateRole(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to update role %s: %w", roleName, err)
	}

	fmt.Printf("Role updated successfully:\n")
	fmt.Printf("  ID: %d, Name: %s\n", updatedRole.ID, updatedRole.Name)
	return nil
}

func RunRoleDelete(c *cli.Context) error {
	roleName := c.String("name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get the role to delete
	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	// Delete the role
	err = queries.DeleteRole(ctx, role.ID)
	if err != nil {
		return fmt.Errorf("failed to delete role %s: %w", roleName, err)
	}

	fmt.Printf("Role %s deleted successfully.\n", roleName)
	return nil
}

func RunRoleGrantProfileAccess(c *cli.Context) error {
	roleName := c.String("role")
	profileName := c.String("profile")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get role and profile
	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	profile, err := queries.GetProfileByName(ctx, profileName)
	if err != nil {
		return fmt.Errorf("failed to get profile %s: %w", profileName, err)
	}

	// Create role-profile association
	params := db.CreateRoleProfileParams{
		RoleID:    role.ID,
		ProfileID: profile.ID,
	}

	_, err = queries.CreateRoleProfile(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to grant profile access %s to role %s: %w", profileName, roleName, err)
	}

	fmt.Printf("Profile access granted: role %s can now access profile %s.\n", roleName, profileName)
	return nil
}

func RunRoleRevokeProfileAccess(c *cli.Context) error {
	roleName := c.String("role")
	profileName := c.String("profile")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get role and profile
	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	profile, err := queries.GetProfileByName(ctx, profileName)
	if err != nil {
		return fmt.Errorf("failed to get profile %s: %w", profileName, err)
	}

	// Remove role-profile association
	params := db.DeleteRoleProfileParams{
		RoleID:    role.ID,
		ProfileID: profile.ID,
	}

	err = queries.DeleteRoleProfile(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to revoke profile access %s from role %s: %w", profileName, roleName, err)
	}

	fmt.Printf("Profile access revoked: role %s can no longer access profile %s.\n", roleName, profileName)
	return nil
}

func RunRoleListProfiles(c *cli.Context) error {
	roleName := c.String("name")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get role
	role, err := queries.GetRoleByName(ctx, roleName)
	if err != nil {
		return fmt.Errorf("failed to get role %s: %w", roleName, err)
	}

	// List profiles accessible by this role
	profiles, err := queries.ListProfilesByRole(ctx, role.ID)
	if err != nil {
		return fmt.Errorf("failed to list profiles for role %s: %w", roleName, err)
	}

	if len(profiles) == 0 {
		fmt.Printf("Role %s has no profile access.\n", roleName)
		return nil
	}

	fmt.Printf("Profiles accessible by role %s:\n", roleName)
	for _, p := range profiles {
		fmt.Printf("  ID: %d, Name: %s, Description: %s, PathSegment: %s\n", p.ID, p.Name, p.Description.String, p.PathSegment)
	}
	return nil
}

// Additional User Management Commands

func RunUserGet(c *cli.Context) error {
	username := c.String("username")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	user, err := queries.GetUserByUsername(ctx, username)
	if err != nil {
		return fmt.Errorf("failed to get user %s: %w", username, err)
	}

	fmt.Printf("User Details:\n")
	fmt.Printf("  ID: %d\n", user.ID)
	fmt.Printf("  Username: %s\n", user.Username)
	fmt.Printf("  Created: %s\n", user.CreatedAt.Time.Format("2006-01-02 15:04:05"))
	fmt.Printf("  Updated: %s\n", user.UpdatedAt.Time.Format("2006-01-02 15:04:05"))
	return nil
}

func RunUserUpdate(c *cli.Context) error {
	username := c.String("username")
	newPassword := c.String("password")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get the existing user
	user, err := queries.GetUserByUsername(ctx, username)
	if err != nil {
		return fmt.Errorf("failed to get user %s: %w", username, err)
	}

	// Hash the new password
	passwordHash, err := bcrypt.GenerateFromPassword([]byte(newPassword), bcrypt.DefaultCost)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// Update the user
	params := db.UpdateUserParams{
		ID:           user.ID,
		PasswordHash: string(passwordHash),
	}

	updatedUser, err := queries.UpdateUser(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to update user %s: %w", username, err)
	}

	fmt.Printf("User %s password updated successfully.\n", updatedUser.Username)
	return nil
}

func RunUserDelete(c *cli.Context) error {
	username := c.String("username")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get the user to delete
	user, err := queries.GetUserByUsername(ctx, username)
	if err != nil {
		return fmt.Errorf("failed to get user %s: %w", username, err)
	}

	// Delete the user
	err = queries.DeleteUser(ctx, user.ID)
	if err != nil {
		return fmt.Errorf("failed to delete user %s: %w", username, err)
	}

	fmt.Printf("User %s deleted successfully.\n", username)
	return nil
}

func RunUserListRoles(c *cli.Context) error {
	username := c.String("username")
	dbConnectionString := c.String("db")

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get user
	user, err := queries.GetUserByUsername(ctx, username)
	if err != nil {
		return fmt.Errorf("failed to get user %s: %w", username, err)
	}

	// List roles assigned to this user
	roles, err := queries.ListRolesByUser(ctx, user.ID)
	if err != nil {
		return fmt.Errorf("failed to list roles for user %s: %w", username, err)
	}

	if len(roles) == 0 {
		fmt.Printf("User %s has no assigned roles.\n", username)
		return nil
	}

	fmt.Printf("Roles assigned to user %s:\n", username)
	for _, r := range roles {
		fmt.Printf("  ID: %d, Name: %s\n", r.ID, r.Name)
	}
	return nil
}

func RunTUI(c *cli.Context) error {
	dbConnectionString := c.String("db")

	// Initialize the Bubble Tea program
	p := tea.NewProgram(tui.InitialModel(dbConnectionString))
	if _, err := p.Run(); err != nil {
		return fmt.Errorf("error running TUI: %w", err)
	}
	return nil
}

// generateSchemaName creates a schema name from the file path
func generateSchemaName(filePath string) string {
	// Get the base filename without extension
	base := filepath.Base(filePath)
	name := strings.TrimSuffix(base, filepath.Ext(base))

	// Replace any non-alphanumeric characters with underscores
	name = strings.ReplaceAll(name, "-", "_")
	name = strings.ReplaceAll(name, " ", "_")

	return name
}

// Connection Management Commands

func RunConnectionCreate(c *cli.Context) error {
	name := c.String("name")
	description := c.String("description")
	baseURL := c.String("base-url")
	authType := c.String("auth-type")
	secret := c.String("secret")
	username := c.String("username")
	headerName := c.String("header-name")
	dbConnectionString := c.String("db")

	// Validate required fields
	if name == "" || baseURL == "" || authType == "" {
		return fmt.Errorf("name, base-url, and auth-type are required")
	}

	// Validate auth type
	validAuthTypes := []string{"none", "basic", "bearer", "custom_header"}
	isValid := false
	for _, valid := range validAuthTypes {
		if authType == valid {
			isValid = true
			break
		}
	}
	if !isValid {
		return fmt.Errorf("invalid auth-type. Must be one of: %s", strings.Join(validAuthTypes, ", "))
	}

	// Validate auth-specific requirements
	switch authType {
	case "basic":
		if username == "" || secret == "" {
			return fmt.Errorf("username and secret are required for basic auth")
		}
	case "bearer":
		if secret == "" {
			return fmt.Errorf("secret (token) is required for bearer auth")
		}
	case "custom_header":
		if headerName == "" || secret == "" {
			return fmt.Errorf("header-name and secret are required for custom_header auth")
		}
	}

	ctx := context.Background()
	queries, cleanup, err := connectDatabase(dbConnectionString)
	if err != nil {
		return err
	}
	defer cleanup()

	// Create connection service
	connService := models.NewConnectionService(queries)

	// Prepare auth details
	auth := util.ConnectionAuth{
		Type: authType,
	}

	switch authType {
	case "basic":
		auth.Username = username
		auth.Password = secret
	case "bearer":
		auth.Token = secret
	case "custom_header":
		auth.HeaderName = headerName
		auth.HeaderValue = secret
	}

	// Create connection
	req := models.CreateConnectionRequest{
		Name:        name,
		Description: description,
		BaseURL:     baseURL,
		Auth:        auth,
	}

	createdConn, err := connService.CreateConnection(ctx, req)
	if err != nil {
		return fmt.Errorf("failed to create connection: %w", err)
	}

	fmt.Printf("Connection created successfully:\n")
	fmt.Printf("  ID: %d\n", createdConn.ID)
	fmt.Printf("  Name: %s\n", createdConn.Name)
	fmt.Printf("  Description: %s\n", createdConn.Description)
	fmt.Printf("  Base URL: %s\n", createdConn.BaseURL)
	fmt.Printf("  Auth Type: %s\n", createdConn.Auth.Type)

	return nil
}

func RunConnectionList(c *cli.Context) error {
	dbConnectionString := c.String("db")

	ctx := context.Background()
	queries, cleanup, err := connectDatabase(dbConnectionString)
	if err != nil {
		return err
	}
	defer cleanup()

	connService := models.NewConnectionService(queries)
	connections, err := connService.ListConnections(ctx)
	if err != nil {
		return fmt.Errorf("failed to list connections: %w", err)
	}

	if len(connections) == 0 {
		fmt.Println("No connections found.")
		return nil
	}

	fmt.Printf("Found %d connection(s):\n", len(connections))
	for _, conn := range connections {
		fmt.Printf("  ID: %d, Name: %s, Base URL: %s, Auth Type: %s\n",
			conn.ID, conn.Name, conn.BaseURL, conn.Auth.Type)
		if conn.Description != "" {
			fmt.Printf("    Description: %s\n", conn.Description)
		}
	}
	return nil
}

func RunConnectionGet(c *cli.Context) error {
	name := c.String("name")
	dbConnectionString := c.String("db")

	if name == "" {
		return fmt.Errorf("connection name is required")
	}

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	connService := models.NewConnectionService(queries)
	connection, err := connService.GetConnection(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to get connection: %w", err)
	}

	fmt.Printf("Connection details:\n")
	fmt.Printf("  ID: %d\n", connection.ID)
	fmt.Printf("  Name: %s\n", connection.Name)
	fmt.Printf("  Description: %s\n", connection.Description)
	fmt.Printf("  Base URL: %s\n", connection.BaseURL)
	fmt.Printf("  Auth Type: %s\n", connection.Auth.Type)

	switch connection.Auth.Type {
	case "basic":
		fmt.Printf("  Username: %s\n", connection.Auth.Username)
		fmt.Printf("  Password: [HIDDEN]\n")
	case "bearer":
		fmt.Printf("  Token: [HIDDEN]\n")
	case "custom_header":
		fmt.Printf("  Header Name: %s\n", connection.Auth.HeaderName)
		fmt.Printf("  Header Value: [HIDDEN]\n")
	}

	return nil
}

func RunConnectionDelete(c *cli.Context) error {
	name := c.String("name")
	dbConnectionString := c.String("db")

	if name == "" {
		return fmt.Errorf("connection name is required")
	}

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	connService := models.NewConnectionService(queries)
	err = connService.DeleteConnection(ctx, name)
	if err != nil {
		return fmt.Errorf("failed to delete connection: %w", err)
	}

	fmt.Printf("Connection '%s' deleted successfully.\n", name)
	return nil
}

// Schema Association Commands

func RunSchemaAssociateConnection(c *cli.Context) error {
	schemaName := c.String("schema")
	connectionName := c.String("connection")
	isDefault := c.Bool("default")
	dbConnectionString := c.String("db")

	if schemaName == "" || connectionName == "" {
		return fmt.Errorf("both schema and connection names are required")
	}

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	schemaService := models.NewSchemaService(queries)
	err = schemaService.AssociateConnection(ctx, schemaName, connectionName, isDefault)
	if err != nil {
		return fmt.Errorf("failed to associate connection: %w", err)
	}

	defaultStr := ""
	if isDefault {
		defaultStr = " (as default)"
	}
	fmt.Printf("Successfully associated connection '%s' with schema '%s'%s.\n",
		connectionName, schemaName, defaultStr)
	return nil
}

func RunSchemaDisassociateConnection(c *cli.Context) error {
	schemaName := c.String("schema")
	connectionName := c.String("connection")
	dbConnectionString := c.String("db")

	if schemaName == "" || connectionName == "" {
		return fmt.Errorf("both schema and connection names are required")
	}

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	schemaService := models.NewSchemaService(queries)
	err = schemaService.DisassociateConnection(ctx, schemaName, connectionName)
	if err != nil {
		return fmt.Errorf("failed to disassociate connection: %w", err)
	}

	fmt.Printf("Successfully disassociated connection '%s' from schema '%s'.\n",
		connectionName, schemaName)
	return nil
}

func RunSchemaListConnections(c *cli.Context) error {
	schemaName := c.String("schema")
	dbConnectionString := c.String("db")

	if schemaName == "" {
		return fmt.Errorf("schema name is required")
	}

	ctx := context.Background()
	conn, err := db.ConnectDB(dbConnectionString)
	if err != nil {
		return fmt.Errorf("failed to connect to database: %w", err)
	}
	defer conn.Close(ctx)
	queries := db.New(conn)

	// Get schema by name
	schema, err := queries.GetOpenAPISchemaByName(ctx, schemaName)
	if err != nil {
		return fmt.Errorf("failed to get schema: %w", err)
	}

	// List connections for schema
	connections, err := queries.ListConnectionsBySchema(ctx, schema.ID)
	if err != nil {
		return fmt.Errorf("failed to list connections: %w", err)
	}

	if len(connections) == 0 {
		fmt.Printf("No connections associated with schema '%s'.\n", schemaName)
		return nil
	}

	fmt.Printf("Connections associated with schema '%s':\n", schemaName)
	for _, conn := range connections {
		fmt.Printf("  - %s (%s)\n", conn.Name, conn.BaseUrl)
	}
	return nil
}
