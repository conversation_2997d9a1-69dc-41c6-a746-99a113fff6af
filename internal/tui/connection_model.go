package tui

import (
	"context"
	"fmt"
	"strings"

	"github.com/charmbracelet/bubbles/textinput"
	"github.com/charmbracelet/bubbletea"
	"github.com/ravan/suse-air/internal/db"
	"github.com/ravan/suse-air/internal/models"
	"github.com/ravan/suse-air/internal/util"
)

type connectionModel struct {
	queries     db.Querier
	connections []models.ConnectionInfo
	schemas     []db.OpenapiSchema
	cursor      int
	selected    int // index of selected connection for details/update/delete
	state       connectionState

	// Input fields for create/update
	nameInput        textinput.Model
	descriptionInput textinput.Model
	baseURLInput     textinput.Model
	authTypeInput    textinput.Model
	usernameInput    textinput.Model
	secretInput      textinput.Model
	headerNameInput  textinput.Model

	// For schema association
	schemaNameInput     textinput.Model
	connectionNameInput textinput.Model

	// Status message
	statusMessage string

	// Services
	connService   *models.ConnectionService
	schemaService *models.SchemaService
}

type connectionState int

const (
	connectionList connectionState = iota
	connectionCreateForm
	connectionDetail
	connectionDeleteConfirm
	schemaAssociationMenu
	schemaAssociateForm
	schemaDisassociateForm
	schemaListConnectionsForm
)

func newConnectionModel(queries db.Querier) connectionModel {
	cm := connectionModel{
		queries:       queries,
		state:         connectionList,
		connService:   models.NewConnectionService(queries),
		schemaService: models.NewSchemaService(queries),
	}
	cm.nameInput = newTextInput("Connection Name", false)
	cm.descriptionInput = newTextInput("Description", false)
	cm.baseURLInput = newTextInput("Base URL", false)
	cm.authTypeInput = newTextInput("Auth Type (none/basic/bearer/custom_header)", false)
	cm.usernameInput = newTextInput("Username", false)
	cm.secretInput = newTextInput("Secret", true) // password field
	cm.headerNameInput = newTextInput("Header Name", false)
	cm.schemaNameInput = newTextInput("Schema Name", false)
	cm.connectionNameInput = newTextInput("Connection Name", false)
	return cm
}

func (cm connectionModel) Init() tea.Cmd {
	return cm.loadConnections()
}

func (cm connectionModel) Update(msg tea.Msg) (tea.Model, tea.Cmd) {
	var cmds []tea.Cmd

	switch msg := msg.(type) {
	case tea.KeyMsg:
		switch cm.state {
		case connectionList:
			return cm.updateConnectionList(msg)
		case connectionCreateForm:
			return cm.updateCreateForm(msg)
		case connectionDetail:
			return cm.updateConnectionDetail(msg)
		case connectionDeleteConfirm:
			return cm.updateDeleteConfirm(msg)
		case schemaAssociationMenu:
			return cm.updateSchemaAssociationMenu(msg)
		case schemaAssociateForm:
			return cm.updateSchemaAssociateForm(msg)
		case schemaDisassociateForm:
			return cm.updateSchemaDisassociateForm(msg)
		case schemaListConnectionsForm:
			return cm.updateSchemaListConnectionsForm(msg)
		}

	case connectionsLoadedMsg:
		cm.connections = msg.connections
		cm.statusMessage = ""
		return cm, nil

	case connectionCreatedMsg:
		cm.statusMessage = "Connection created successfully!"
		cm.state = connectionList
		cm.clearInputs()
		return cm, cm.loadConnections()

	case connectionDeletedMsg:
		cm.statusMessage = "Connection deleted successfully!"
		cm.state = connectionList
		return cm, cm.loadConnections()

	case schemasLoadedMsg:
		cm.schemas = msg.schemas
		return cm, nil

	case statusMsg:
		cm.statusMessage = string(msg)
		return cm, nil

	case errorMsg:
		cm.statusMessage = fmt.Sprintf("Error: %s", string(msg))
		return cm, nil
	}

	return cm, tea.Batch(cmds...)
}

func (cm connectionModel) View() string {
	switch cm.state {
	case connectionList:
		return cm.viewConnectionList()
	case connectionCreateForm:
		return cm.viewCreateForm()
	case connectionDetail:
		return cm.viewConnectionDetail()
	case connectionDeleteConfirm:
		return cm.viewDeleteConfirm()
	case schemaAssociationMenu:
		return cm.viewSchemaAssociationMenu()
	case schemaAssociateForm:
		return cm.viewSchemaAssociateForm()
	case schemaDisassociateForm:
		return cm.viewSchemaDisassociateForm()
	case schemaListConnectionsForm:
		return cm.viewSchemaListConnectionsForm()
	}
	return ""
}

func (cm connectionModel) viewConnectionList() string {
	s := "Connection Management\n\n"

	if cm.statusMessage != "" {
		s += fmt.Sprintf("Status: %s\n\n", cm.statusMessage)
	}

	if len(cm.connections) == 0 {
		s += "No connections found.\n\n"
	} else {
		s += "Connections:\n"
		for i, conn := range cm.connections {
			cursor := " "
			if cm.cursor == i {
				cursor = ">"
			}
			s += fmt.Sprintf("%s %s (%s) - %s\n", cursor, conn.Name, conn.Auth.Type, conn.BaseURL)
		}
		s += "\n"
	}

	s += "Commands:\n"
	s += "  n - Create new connection\n"
	s += "  enter - View connection details\n"
	s += "  d - Delete connection\n"
	s += "  s - Schema associations\n"
	s += "  r - Refresh list\n"
	s += "  esc - Back to main menu\n"

	return s
}

func (cm connectionModel) viewCreateForm() string {
	s := "Create New Connection\n\n"

	s += fmt.Sprintf("Name: %s\n", cm.nameInput.View())
	s += fmt.Sprintf("Description: %s\n", cm.descriptionInput.View())
	s += fmt.Sprintf("Base URL: %s\n", cm.baseURLInput.View())
	s += fmt.Sprintf("Auth Type: %s\n", cm.authTypeInput.View())

	authType := cm.authTypeInput.Value()
	switch authType {
	case "basic":
		s += fmt.Sprintf("Username: %s\n", cm.usernameInput.View())
		s += fmt.Sprintf("Password: %s\n", cm.secretInput.View())
	case "bearer":
		s += fmt.Sprintf("Token: %s\n", cm.secretInput.View())
	case "custom_header":
		s += fmt.Sprintf("Header Name: %s\n", cm.headerNameInput.View())
		s += fmt.Sprintf("Header Value: %s\n", cm.secretInput.View())
	}

	s += "\nPress Tab to move between fields, Enter to create, Esc to cancel\n"

	if cm.statusMessage != "" {
		s += fmt.Sprintf("\nStatus: %s\n", cm.statusMessage)
	}

	return s
}

func (cm connectionModel) viewConnectionDetail() string {
	if cm.selected >= len(cm.connections) {
		return "Connection not found\n\nPress esc to go back"
	}

	conn := cm.connections[cm.selected]
	s := fmt.Sprintf("Connection Details: %s\n\n", conn.Name)
	s += fmt.Sprintf("Description: %s\n", conn.Description)
	s += fmt.Sprintf("Base URL: %s\n", conn.BaseURL)
	s += fmt.Sprintf("Auth Type: %s\n", conn.Auth.Type)

	switch conn.Auth.Type {
	case "basic":
		s += fmt.Sprintf("Username: %s\n", conn.Auth.Username)
		s += "Password: [HIDDEN]\n"
	case "bearer":
		s += "Token: [HIDDEN]\n"
	case "custom_header":
		s += fmt.Sprintf("Header Name: %s\n", conn.Auth.HeaderName)
		s += "Header Value: [HIDDEN]\n"
	}

	s += "\nPress esc to go back"
	return s
}

func (cm connectionModel) viewDeleteConfirm() string {
	if cm.selected >= len(cm.connections) {
		return "Connection not found\n\nPress esc to go back"
	}

	conn := cm.connections[cm.selected]
	s := fmt.Sprintf("Delete Connection: %s\n\n", conn.Name)
	s += "Are you sure you want to delete this connection?\n"
	s += "This action cannot be undone.\n\n"
	s += "Press 'y' to confirm, any other key to cancel"
	return s
}

func (cm connectionModel) viewSchemaAssociationMenu() string {
	s := "Schema Association Management\n\n"

	if cm.statusMessage != "" {
		s += fmt.Sprintf("Status: %s\n\n", cm.statusMessage)
	}

	s += "Choose an action:\n\n"
	s += "1. Associate connection with schema\n"
	s += "2. Disassociate connection from schema\n"
	s += "3. List connections for schema\n"
	s += "\nPress 1-3 to select, esc to go back"

	return s
}

func (cm connectionModel) viewSchemaAssociateForm() string {
	s := "Associate Connection with Schema\n\n"
	s += fmt.Sprintf("Schema Name: %s\n", cm.schemaNameInput.View())
	s += fmt.Sprintf("Connection Name: %s\n", cm.connectionNameInput.View())
	s += "\nPress Tab to move between fields, Enter to associate, Esc to cancel\n"

	if cm.statusMessage != "" {
		s += fmt.Sprintf("\nStatus: %s\n", cm.statusMessage)
	}

	return s
}

func (cm connectionModel) viewSchemaDisassociateForm() string {
	s := "Disassociate Connection from Schema\n\n"
	s += fmt.Sprintf("Schema Name: %s\n", cm.schemaNameInput.View())
	s += fmt.Sprintf("Connection Name: %s\n", cm.connectionNameInput.View())
	s += "\nPress Tab to move between fields, Enter to disassociate, Esc to cancel\n"

	if cm.statusMessage != "" {
		s += fmt.Sprintf("\nStatus: %s\n", cm.statusMessage)
	}

	return s
}

// Update methods for different states

func (cm connectionModel) updateConnectionList(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "up", "k":
		if cm.cursor > 0 {
			cm.cursor--
		}
	case "down", "j":
		if cm.cursor < len(cm.connections)-1 {
			cm.cursor++
		}
	case "n":
		cm.state = connectionCreateForm
		cm.clearInputs()
		cm.nameInput.Focus()
		return cm, nil
	case "enter":
		if len(cm.connections) > 0 && cm.cursor < len(cm.connections) {
			cm.selected = cm.cursor
			cm.state = connectionDetail
		}
		return cm, nil
	case "d":
		if len(cm.connections) > 0 && cm.cursor < len(cm.connections) {
			cm.selected = cm.cursor
			cm.state = connectionDeleteConfirm
		}
		return cm, nil
	case "s":
		cm.state = schemaAssociationMenu
		return cm, cm.loadSchemas()
	case "r":
		return cm, cm.loadConnections()
	}
	return cm, nil
}

func (cm connectionModel) updateCreateForm(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "esc":
		cm.state = connectionList
		cm.clearInputs()
		return cm, nil
	case "tab":
		return cm.focusNextInput(), nil
	case "enter":
		return cm, cm.createConnection()
	}

	// Update the focused input
	var cmd tea.Cmd
	if cm.nameInput.Focused() {
		cm.nameInput, cmd = cm.nameInput.Update(msg)
	} else if cm.descriptionInput.Focused() {
		cm.descriptionInput, cmd = cm.descriptionInput.Update(msg)
	} else if cm.baseURLInput.Focused() {
		cm.baseURLInput, cmd = cm.baseURLInput.Update(msg)
	} else if cm.authTypeInput.Focused() {
		cm.authTypeInput, cmd = cm.authTypeInput.Update(msg)
	} else if cm.usernameInput.Focused() {
		cm.usernameInput, cmd = cm.usernameInput.Update(msg)
	} else if cm.secretInput.Focused() {
		cm.secretInput, cmd = cm.secretInput.Update(msg)
	} else if cm.headerNameInput.Focused() {
		cm.headerNameInput, cmd = cm.headerNameInput.Update(msg)
	}

	return cm, cmd
}

func (cm connectionModel) updateConnectionDetail(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "esc":
		cm.state = connectionList
		return cm, nil
	}
	return cm, nil
}

func (cm connectionModel) updateDeleteConfirm(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "y":
		return cm, cm.deleteConnection()
	default:
		cm.state = connectionList
		return cm, nil
	}
}

func (cm connectionModel) updateSchemaAssociationMenu(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "esc":
		cm.state = connectionList
		return cm, nil
	case "1":
		cm.state = schemaAssociateForm
		cm.clearSchemaInputs()
		cm.schemaNameInput.Focus()
		return cm, nil
	case "2":
		cm.state = schemaDisassociateForm
		cm.clearSchemaInputs()
		cm.schemaNameInput.Focus()
		return cm, nil
	case "3":
		cm.state = schemaListConnectionsForm
		cm.clearSchemaInputs()
		cm.schemaNameInput.Focus()
		return cm, nil
	}
	return cm, nil
}

func (cm connectionModel) updateSchemaAssociateForm(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "esc":
		cm.state = schemaAssociationMenu
		cm.clearSchemaInputs()
		return cm, nil
	case "tab":
		return cm.focusNextSchemaInput(), nil
	case "enter":
		return cm, cm.associateConnection()
	}

	var cmd tea.Cmd
	if cm.schemaNameInput.Focused() {
		cm.schemaNameInput, cmd = cm.schemaNameInput.Update(msg)
	} else if cm.connectionNameInput.Focused() {
		cm.connectionNameInput, cmd = cm.connectionNameInput.Update(msg)
	}

	return cm, cmd
}

func (cm connectionModel) updateSchemaDisassociateForm(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "esc":
		cm.state = schemaAssociationMenu
		cm.clearSchemaInputs()
		return cm, nil
	case "tab":
		return cm.focusNextSchemaInput(), nil
	case "enter":
		return cm, cm.disassociateConnection()
	}

	var cmd tea.Cmd
	if cm.schemaNameInput.Focused() {
		cm.schemaNameInput, cmd = cm.schemaNameInput.Update(msg)
	} else if cm.connectionNameInput.Focused() {
		cm.connectionNameInput, cmd = cm.connectionNameInput.Update(msg)
	}

	return cm, cmd
}

func (cm connectionModel) updateSchemaListConnectionsForm(msg tea.KeyMsg) (tea.Model, tea.Cmd) {
	switch msg.String() {
	case "esc":
		cm.state = schemaAssociationMenu
		cm.clearSchemaInputs()
		return cm, nil
	case "enter":
		return cm, cm.listSchemaConnections()
	}

	var cmd tea.Cmd
	cm.schemaNameInput, cmd = cm.schemaNameInput.Update(msg)
	return cm, cmd
}

// Helper methods

func (cm *connectionModel) clearInputs() {
	cm.nameInput.SetValue("")
	cm.descriptionInput.SetValue("")
	cm.baseURLInput.SetValue("")
	cm.authTypeInput.SetValue("")
	cm.usernameInput.SetValue("")
	cm.secretInput.SetValue("")
	cm.headerNameInput.SetValue("")
	cm.statusMessage = ""
}

func (cm *connectionModel) clearSchemaInputs() {
	cm.schemaNameInput.SetValue("")
	cm.connectionNameInput.SetValue("")
	cm.statusMessage = ""
}

func (cm connectionModel) focusNextInput() connectionModel {
	if cm.nameInput.Focused() {
		cm.nameInput.Blur()
		cm.descriptionInput.Focus()
	} else if cm.descriptionInput.Focused() {
		cm.descriptionInput.Blur()
		cm.baseURLInput.Focus()
	} else if cm.baseURLInput.Focused() {
		cm.baseURLInput.Blur()
		cm.authTypeInput.Focus()
	} else if cm.authTypeInput.Focused() {
		cm.authTypeInput.Blur()
		authType := cm.authTypeInput.Value()
		switch authType {
		case "basic":
			cm.usernameInput.Focus()
		case "bearer":
			cm.secretInput.Focus()
		case "custom_header":
			cm.headerNameInput.Focus()
		default:
			cm.nameInput.Focus()
		}
	} else if cm.usernameInput.Focused() {
		cm.usernameInput.Blur()
		cm.secretInput.Focus()
	} else if cm.secretInput.Focused() {
		cm.secretInput.Blur()
		cm.nameInput.Focus()
	} else if cm.headerNameInput.Focused() {
		cm.headerNameInput.Blur()
		cm.secretInput.Focus()
	} else {
		cm.nameInput.Focus()
	}
	return cm
}

func (cm connectionModel) focusNextSchemaInput() connectionModel {
	if cm.schemaNameInput.Focused() {
		cm.schemaNameInput.Blur()
		cm.connectionNameInput.Focus()
	} else {
		cm.connectionNameInput.Blur()
		cm.schemaNameInput.Focus()
	}
	return cm
}

// Commands

func (cm connectionModel) loadConnections() tea.Cmd {
	return func() tea.Msg {
		connections, err := cm.connService.ListConnections(context.Background())
		if err != nil {
			return errorMsg(err.Error())
		}
		return connectionsLoadedMsg{connections: connections}
	}
}

func (cm connectionModel) loadSchemas() tea.Cmd {
	return func() tea.Msg {
		schemas, err := cm.queries.ListOpenAPISchemas(context.Background())
		if err != nil {
			return errorMsg(err.Error())
		}
		return schemasLoadedMsg{schemas: schemas}
	}
}

func (cm connectionModel) createConnection() tea.Cmd {
	return func() tea.Msg {
		// Validate inputs
		name := strings.TrimSpace(cm.nameInput.Value())
		baseURL := strings.TrimSpace(cm.baseURLInput.Value())
		authType := strings.TrimSpace(cm.authTypeInput.Value())

		if name == "" || baseURL == "" || authType == "" {
			return errorMsg("Name, Base URL, and Auth Type are required")
		}

		// Validate auth type
		validAuthTypes := []string{"none", "basic", "bearer", "custom_header"}
		isValid := false
		for _, valid := range validAuthTypes {
			if authType == valid {
				isValid = true
				break
			}
		}
		if !isValid {
			return errorMsg("Invalid auth type. Must be one of: none, basic, bearer, custom_header")
		}

		// Create auth object
		auth := util.ConnectionAuth{Type: authType}
		switch authType {
		case "basic":
			username := strings.TrimSpace(cm.usernameInput.Value())
			password := cm.secretInput.Value()
			if username == "" || password == "" {
				return errorMsg("Username and password are required for basic auth")
			}
			auth.Username = username
			auth.Password = password
		case "bearer":
			token := cm.secretInput.Value()
			if token == "" {
				return errorMsg("Token is required for bearer auth")
			}
			auth.Token = token
		case "custom_header":
			headerName := strings.TrimSpace(cm.headerNameInput.Value())
			headerValue := cm.secretInput.Value()
			if headerName == "" || headerValue == "" {
				return errorMsg("Header name and value are required for custom header auth")
			}
			auth.HeaderName = headerName
			auth.HeaderValue = headerValue
		}

		// Create connection
		req := models.CreateConnectionRequest{
			Name:        name,
			Description: strings.TrimSpace(cm.descriptionInput.Value()),
			BaseURL:     baseURL,
			Auth:        auth,
		}

		_, err := cm.connService.CreateConnection(context.Background(), req)
		if err != nil {
			return errorMsg(err.Error())
		}

		return connectionCreatedMsg{}
	}
}

func (cm connectionModel) deleteConnection() tea.Cmd {
	return func() tea.Msg {
		if cm.selected >= len(cm.connections) {
			return errorMsg("Invalid connection selected")
		}

		conn := cm.connections[cm.selected]
		err := cm.connService.DeleteConnection(context.Background(), conn.Name)
		if err != nil {
			return errorMsg(err.Error())
		}

		return connectionDeletedMsg{}
	}
}

func (cm connectionModel) associateConnection() tea.Cmd {
	return func() tea.Msg {
		schemaName := strings.TrimSpace(cm.schemaNameInput.Value())
		connectionName := strings.TrimSpace(cm.connectionNameInput.Value())

		if schemaName == "" || connectionName == "" {
			return errorMsg("Schema name and connection name are required")
		}

		err := cm.schemaService.AssociateConnection(context.Background(), schemaName, connectionName, true)
		if err != nil {
			return errorMsg(err.Error())
		}

		return statusMsg("Connection associated successfully!")
	}
}

func (cm connectionModel) disassociateConnection() tea.Cmd {
	return func() tea.Msg {
		schemaName := strings.TrimSpace(cm.schemaNameInput.Value())
		connectionName := strings.TrimSpace(cm.connectionNameInput.Value())

		if schemaName == "" || connectionName == "" {
			return errorMsg("Schema name and connection name are required")
		}

		err := cm.schemaService.DisassociateConnection(context.Background(), schemaName, connectionName)
		if err != nil {
			return errorMsg(err.Error())
		}

		return statusMsg("Connection disassociated successfully!")
	}
}

func (cm connectionModel) listSchemaConnections() tea.Cmd {
	return func() tea.Msg {
		schemaName := strings.TrimSpace(cm.schemaNameInput.Value())

		if schemaName == "" {
			return errorMsg("Schema name is required")
		}

		// Get schema by name
		schema, err := cm.queries.GetOpenAPISchemaByName(context.Background(), schemaName)
		if err != nil {
			return errorMsg(fmt.Sprintf("Schema not found: %v", err))
		}

		// List connections for schema
		connections, err := cm.queries.ListConnectionsBySchema(context.Background(), schema.ID)
		if err != nil {
			return errorMsg(err.Error())
		}

		if len(connections) == 0 {
			return statusMsg(fmt.Sprintf("No connections associated with schema '%s'", schemaName))
		}

		var connNames []string
		for _, conn := range connections {
			connNames = append(connNames, conn.Name)
		}

		return statusMsg(fmt.Sprintf("Connections for schema '%s': %s", schemaName, strings.Join(connNames, ", ")))
	}
}

// Message types

type connectionsLoadedMsg struct {
	connections []models.ConnectionInfo
}

type schemasLoadedMsg struct {
	schemas []db.OpenapiSchema
}

type connectionCreatedMsg struct{}

type connectionDeletedMsg struct{}

type statusMsg string

type errorMsg string

func (cm connectionModel) viewSchemaListConnectionsForm() string {
	s := "List Connections for Schema\n\n"
	s += fmt.Sprintf("Schema Name: %s\n", cm.schemaNameInput.View())
	s += "\nPress Enter to list connections, Esc to cancel\n"

	if cm.statusMessage != "" {
		s += fmt.Sprintf("\nStatus: %s\n", cm.statusMessage)
	}

	return s
}
