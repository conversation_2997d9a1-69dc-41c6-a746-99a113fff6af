#!/bin/bash

# <PERSON><PERSON><PERSON> to fix remaining CLI test issues

echo "Fixing CLI test issues..."

for file in tests/unit/cli/*.go; do
    if [ -f "$file" ]; then
        echo "Updating $file..."
        
        # Fix cli.NewFlagSet to flag.NewFlagSet
        sed -i '' 's/cli\.NewFlagSet/flag.NewFlagSet/g' "$file"
        
        # Fix MockQuerier to MockDBQueries
        sed -i '' 's/MockQuerier/MockDBQueries/g' "$file"
        
        # Remove invalid MockConnectDB references
        sed -i '' '/test_utils\.MockConnectDB/d' "$file"
        sed -i '' '/originalConnectDB.*MockConnectDB/d' "$file"
        sed -i '' '/MockConnectDB.*originalConnectDB/d' "$file"
        
        # Add flag import if not present
        if ! grep -q '"flag"' "$file"; then
            sed -i '' '/import (/a\
	"flag"
' "$file"
        fi
    fi
done

echo "CLI test issues fixed!"
