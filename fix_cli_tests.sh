#!/bin/bash

# <PERSON><PERSON><PERSON> to fix CLI test files by removing invalid db.New assignments

echo "Fixing CLI test files..."

for file in tests/unit/cli/*.go; do
    if [ -f "$file" ]; then
        echo "Updating $file..."
        
        # Replace the invalid db.New assignments with comments
        sed -i '' 's/oldNew := db\.New/\/\/ Note: Cannot mock db.New directly/g' "$file"
        sed -i '' 's/defer func() { db\.New = oldNew }()/\/\/ This is a limitation of the current test setup/g' "$file"
        sed -i '' 's/db\.New = func(conn db\.DBTX) \*db\.Queries { return dbQueries }/\/\/ Would need to restructure to properly mock database layer/g' "$file"
    fi
done

echo "CLI test files fixed!"
