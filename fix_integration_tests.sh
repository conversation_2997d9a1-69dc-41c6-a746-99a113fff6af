#!/bin/bash

# <PERSON>ript to fix integration tests to work with the new database abstraction
# This removes calls to conn.<PERSON>() and conn.Ping() which are not available on DBTX interface

echo "Fixing integration test files..."

# Find all Go files in tests/integration that contain conn.Close
for file in $(grep -l "conn\.Close" tests/integration/*.go); do
    echo "Fixing $file..."
    
    # Create a backup
    cp "$file" "$file.backup"
    
    # Remove defer conn.Close(context.Background()) lines
    sed -i '' '/defer conn\.Close(context\.Background())/d' "$file"
    
    # Remove defer conn.Close(ctx) lines
    sed -i '' '/defer conn\.Close(ctx)/d' "$file"
    
    # Remove standalone conn.Close(context.Background()) lines
    sed -i '' '/[[:space:]]*conn\.Close(context\.Background())/d' "$file"
    
    # Remove conn.Ping() calls
    sed -i '' '/conn\.Ping(/d' "$file"
    
    echo "Fixed $file"
done

echo "Integration test files fixed!"
echo "Backup files created with .backup extension"
