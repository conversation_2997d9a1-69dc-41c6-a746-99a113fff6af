#!/bin/bash

# <PERSON>ript to update all database connection patterns in commands.go
# This replaces the old pattern with the new abstracted pattern

FILE="internal/cmd/commands.go"

# Create a backup
cp "$FILE" "$FILE.backup"

# Replace the pattern using sed
sed -i '' 's/ctx := context\.Background()/ctx := context.Background()/g' "$FILE"
sed -i '' '/conn, err := db\.ConnectDB(dbConnectionString)/,/queries := db\.New(conn)/ {
    /conn, err := db\.ConnectDB(dbConnectionString)/ {
        c\
	queries, cleanup, err := connectDatabase(dbConnectionString)\
	if err != nil {\
		return err\
	}\
	defer cleanup()
    }
    /if err != nil {/d
    /return fmt\.Errorf("failed to connect to database: %w", err)/d
    /}/d
    /defer conn\.Close(ctx)/d
    /queries := db\.New(conn)/d
}' "$FILE"

echo "Database connection patterns updated in $FILE"
echo "Backup saved as $FILE.backup"
