#!/bin/bash

# <PERSON>ript to fix CLI function calls to use cmd package prefix

echo "Fixing CLI function calls..."

for file in tests/unit/cli/*.go; do
    if [ -f "$file" ]; then
        echo "Updating $file..."
        
        # Fix function calls to use cmd package prefix
        sed -i '' 's/runUserCreate(/cmd.RunUserCreate(/g' "$file"
        sed -i '' 's/runUserList(/cmd.RunUserList(/g' "$file"
        sed -i '' 's/runUserGet(/cmd.RunUserGet(/g' "$file"
        sed -i '' 's/runUserUpdate(/cmd.RunUserUpdate(/g' "$file"
        sed -i '' 's/runUserDelete(/cmd.RunUserDelete(/g' "$file"
        sed -i '' 's/runUserAssignRole(/cmd.RunUserAssignRole(/g' "$file"
        sed -i '' 's/runUserRemoveRole(/cmd.RunUserRemoveRole(/g' "$file"
        
        sed -i '' 's/runRoleCreate(/cmd.RunRoleCreate(/g' "$file"
        sed -i '' 's/runRoleList(/cmd.RunRoleList(/g' "$file"
        sed -i '' 's/runRoleGet(/cmd.RunRoleGet(/g' "$file"
        sed -i '' 's/runRoleUpdate(/cmd.RunRoleUpdate(/g' "$file"
        sed -i '' 's/runRoleDelete(/cmd.RunRoleDelete(/g' "$file"
        sed -i '' 's/runRoleGrantProfile(/cmd.RunRoleGrantProfile(/g' "$file"
        sed -i '' 's/runRoleRevokeProfile(/cmd.RunRoleRevokeProfile(/g' "$file"
        
        sed -i '' 's/runToolCreate(/cmd.RunToolCreate(/g' "$file"
        sed -i '' 's/runToolList(/cmd.RunToolList(/g' "$file"
        sed -i '' 's/runToolGet(/cmd.RunToolGet(/g' "$file"
        sed -i '' 's/runToolUpdate(/cmd.RunToolUpdate(/g' "$file"
        sed -i '' 's/runToolDelete(/cmd.RunToolDelete(/g' "$file"
        sed -i '' 's/runToolAssociate(/cmd.RunToolAssociate(/g' "$file"
        sed -i '' 's/runToolDisassociate(/cmd.RunToolDisassociate(/g' "$file"
    fi
done

echo "CLI function calls fixed!"
